"use client"

import React, { useState } from "react";
import { TableWrapper, Table, TableRow, TableCell } from "@/components/common/table";
import { cn } from "@/lib/utils";
import ViewModal from "@/components/common/view";
import { Eye, Edit, Trash2 } from "lucide-react";
import { FormModal } from "@/components/common/form";
import { ConfirmDialog } from "@/components/common/dialog";

const vehicleTypeIcons = {
  Trailer: "🚛",
  Dumper: "🚚",
  Tanker: "🛢️",
  Containerized: "📦",
};

const docStatusIcons = {
  Uploaded: "✅",
  Expired: "⚠️",
};

export default function VehicleDetailsSection() {
  // Demo data
  const [selectedVehicle, setSelectedVehicle] = useState(null);
  const [editVehicle, setEditVehicle] = useState(null);
  const [deleteVehicle, setDeleteVehicle] = useState(null);

  const vehicles = [
    {
      id: 1,
      number: "MH12AB1234",
      type: "Trailer",
      capacity: 25,
      owner: "FastMove Logistics",
      driver: "S. Yadav",
      driverMobile: "9001122334",
      route: "Mumbai → Pune",
      status: "Available",
      lastMaint: "2025-06-20",
      gps: "Active",
      rc: "Uploaded",
      insurance: "Uploaded",
      puc: "Expired",
    },
    {
      id: 2,
      number: "GJ05XY5678",
      type: "Dumper",
      capacity: 18,
      owner: "CargoXpress",
      driver: "A. Patel",
      driverMobile: "9009988776",
      route: "Kandla → Mundra",
      status: "In Use",
      lastMaint: "2025-06-10",
      gps: "Inactive",
      rc: "Uploaded",
      insurance: "Expired",
      puc: "Uploaded",
    },
    {
      id: 3,
      number: "UP78CD9012",
      type: "Tanker",
      capacity: 30,
      owner: "Liquid Movers",
      driver: "R. Sharma",
      driverMobile: "9005544332",
      route: "Delhi → Jaipur",
      status: "Under Maintenance",
      lastMaint: "2025-05-01",
      gps: "Active",
      rc: "Uploaded",
      insurance: "Uploaded",
      puc: "Uploaded",
    },
    {
      id: 4,
      number: "KA01EF3456",
      type: "Containerized",
      capacity: 20,
      owner: "Global Freight",
      driver: "P. Kumar",
      driverMobile: "9007766554",
      route: "Bangalore → Chennai",
      status: "Available",
      lastMaint: "2025-06-15",
      gps: "Active",
      rc: "Uploaded",
      insurance: "Uploaded",
      puc: "Expired",
    },
    {
      id: 5,
      number: "PB03GH7890",
      type: "Trailer",
      capacity: 28,
      owner: "Punjab Roadways",
      driver: "J. Singh",
      driverMobile: "9003322110",
      route: "Amritsar → Ludhiana",
      status: "In Use",
      lastMaint: "2025-06-05",
      gps: "Inactive",
      rc: "Expired",
      insurance: "Uploaded",
      puc: "Uploaded",
    },
  ];

  const statusColors = { Available: "bg-green-100 text-green-800", "In Use": "bg-blue-100 text-blue-800", "Under Maintenance": "bg-red-100 text-red-800" };

  const handleEdit = (updated) => {
    // Logic to handle edit, if needed
    setEditVehicle(null);
  };

  const handleDelete = (idx) => {
    // Logic to handle delete, if needed
    setDeleteVehicle(null);
  };

  return (
    <>
      <div>
        <TableWrapper
          title="🚚 Vehicle Details"
          headers={[
            "Vehicle",
            "Type",
            "Owner",
            "Status",
            "Actions",
          ]}
        >
          {vehicles.map((v, idx) => (
            <TableRow key={v.id}>
              <TableCell className="font-semibold">{v.number}</TableCell>
              <TableCell>{vehicleTypeIcons[v.type]} {v.type}</TableCell>
              <TableCell>{v.owner}</TableCell>
              <TableCell><span className={cn("px-2 py-1 rounded text-xs font-medium", statusColors[v.status] || "bg-gray-100 text-gray-700")}>{v.status}</span></TableCell>
              <TableCell>
                <div className="flex gap-2 justify-center">
                  <button
                    onClick={() => setSelectedVehicle(v)}
                    className="text-purple-500 hover:bg-purple-100 hover:text-purple-700 rounded-full p-2"
                    title="View"
                  >
                    <Eye className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => setEditVehicle({ ...v, idx })}
                    className="text-green-500 hover:bg-green-100 hover:text-green-700 rounded-full p-2"
                    title="Edit"
                  >
                    <Edit className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => setDeleteVehicle({ ...v, idx })}
                    className="text-red-500 hover:bg-red-100 hover:text-red-700 rounded-full p-2"
                    title="Delete"
                  >
                    <Trash2 className="w-5 h-5" />
                  </button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableWrapper>
      </div>
      {selectedVehicle && (
        <ViewModal
          object={selectedVehicle}
          onClose={() => setSelectedVehicle(null)}
          title="Vehicle Details"
        />
      )}
      {/* Edit Modal (reuse VehicleForm in modal) */}
      {editVehicle && (
        <FormModal
          open={!!editVehicle}
          onClose={() => setEditVehicle(null)}
          title="Edit Vehicle"
        >
          {/* Assuming you have a VehicleForm component similar to VesselForm */}
          {/* <VehicleForm
            onAdd={(updated) => handleEdit({ ...updated, idx: editVehicle.idx })}
            initialData={editVehicle}
            onClose={() => setEditVehicle(null)}
            isEdit
          /> */}
          <p>Vehicle edit form goes here.</p>
        </FormModal>
      )}
      {/* Delete Dialog */}
      {deleteVehicle && (
        <ConfirmDialog
          open={!!deleteVehicle}
          title="Delete Vehicle"
          message={`Are you sure you want to delete "${deleteVehicle.number}"? This action cannot be undone.`}
          onCancel={() => setDeleteVehicle(null)}
          onConfirm={() => handleDelete(deleteVehicle.idx)}
        />
      )}
    </>
  );
}
