import React from "react";
import { Dialog as UIDialog } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

export const ConfirmDialog = ({
  open,
  onConfirm,
  onCancel,
  title,
  message,
  confirmText = "Delete",
  cancelText = "Cancel",
  confirmClass = "bg-red-600 text-white",
  cancelClass = "bg-purple-100 text-purple-700",
}) => {
  return (
    <UIDialog open={open} onClose={onCancel}>
      <div className="p-2">
        <h2 className="text-xl font-bold text-red-600 mb-4">{title}</h2>
        <p className="mb-6 text-purple-900">{message}</p>
        <div className="flex justify-end gap-2">
          <Button variant="ghost" onClick={onCancel} className={cancelClass}>
            {cancelText}
          </Button>
          <Button
            variant="destructive"
            onClick={onConfirm}
            className={confirmClass}
          >
            {confirmText}
          </Button>
        </div>
      </div>
    </UIDialog>
  );
};
