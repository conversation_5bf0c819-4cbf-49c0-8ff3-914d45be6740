"use client";

import React, { useState } from "react";
import { Plus, Search, Filter } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import SitesTable from "./SitesTable";
import SiteFilters from "./SiteFilters";
import { FormModal } from "@/components/common/form";
import SiteForm from "./SiteForm";

export default function SiteOperationsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState({
    type: "",
    status: ""
  });
  const [showFilters, setShowFilters] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [sites, setSites] = useState([]);

  const handleAddSite = (newSite) => {
    setSites(prev => [...prev, { ...newSite, id: Date.now() }]);
    setModalOpen(false);
  };

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
  };

  return (
    <div>
      {/* Page Header */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-4xl font-extrabold text-indigo-800 flex items-center gap-3 border-b-2 border-indigo-200 pb-4">
          🏭 Site Operations
        </h1>
        <Button
          onClick={() => setModalOpen(true)}
          className="flex items-center gap-2 bg-purple-700 hover:bg-purple-800"
        >
          <Plus className="w-4 h-4" />
          Add New Site
        </Button>
      </div>

      {/* Search and Filter Controls */}
      <div className="bg-white rounded-2xl shadow-xl p-6 mb-6 border border-purple-100">
        <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center">
          {/* Search Bar */}
          <div className="flex-1 max-w-md">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-purple-400 w-4 h-4" />
              <Input
                type="text"
                placeholder="Search sites..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-white border-purple-200 focus:border-purple-400 focus:ring-purple-400"
              />
            </div>
          </div>

          {/* Filter Toggle */}
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2 border-purple-200 text-purple-700 hover:bg-purple-50"
          >
            <Filter className="w-4 h-4" />
            Filters
          </Button>
        </div>

        {/* Expandable Filters */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-purple-100">
            <SiteFilters filters={filters} onFilterChange={handleFilterChange} />
          </div>
        )}
      </div>

      {/* Sites Table */}
      <SitesTable
        searchTerm={searchTerm}
        filters={filters}
        sites={sites}
        setSites={setSites}
      />

      {/* Add Site Modal */}
      <FormModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        title="Add New Site"
      >
        <SiteForm onAdd={handleAddSite} />
      </FormModal>
    </div>
  );
}