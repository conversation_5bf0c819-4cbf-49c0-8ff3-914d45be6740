"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import {
  ArrowLeft,
  Building2,
  MapPin,
  User,
  Clock,
  Phone,
  Mail,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import BranchDetailsTabs from "./BranchDetailsTabs";

const BranchDetailsPage = () => {
  const router = useRouter();
  const params = useParams();
  const [branch, setBranch] = useState(null);
  const [loading, setLoading] = useState(true);

  // Mock data - in real app, this would fetch from API
  const mockBranches = {
    1: {
      id: 1,
      branchName: "Mumbai Port Terminal",
      location: "Mumbai, Maharashtra",
      branchType: "Terminal",
      managerName: "Rajesh Kumar",
      contactPhone: "+91-22-1234-5678",
      contactEmail: "<EMAIL>",
      status: "Active",
      lastActivity: "2024-01-08",
      operationalHours: "24/7",
      coordinates: "19.0760, 72.8777",
      description:
        "Main container terminal handling international cargo operations with state-of-the-art facilities",
      capacity: "50,000 TEU",
      area: "25 hectares",
      established: "2015",
      totalStaff: 245,
      equipmentCount: 18,
    },
    2: {
      id: 2,
      branchName: "Chennai Container Yard",
      location: "Chennai, Tamil Nadu",
      branchType: "Yard",
      managerName: "Priya Sharma",
      contactPhone: "+91-44-1234-5678",
      contactEmail: "<EMAIL>",
      status: "Active",
      lastActivity: "2024-01-07",
      operationalHours: "06:00 - 22:00",
      coordinates: "13.0827, 80.2707",
      description:
        "Container storage and handling yard for domestic operations",
      capacity: "30,000 TEU",
      area: "15 hectares",
      established: "2018",
      totalStaff: 156,
      equipmentCount: 12,
    },
    3: {
      id: 3,
      branchName: "Kolkata Cargo Depot",
      location: "Kolkata, West Bengal",
      branchType: "Depot",
      managerName: "Amit Singh",
      contactPhone: "+91-33-1234-5678",
      contactEmail: "<EMAIL>",
      status: "Under Maintenance",
      lastActivity: "2024-01-05",
      operationalHours: "08:00 - 18:00",
      coordinates: "22.5726, 88.3639",
      description: "General cargo depot for break-bulk operations",
      capacity: "20,000 MT",
      area: "10 hectares",
      established: "2012",
      totalStaff: 89,
      equipmentCount: 8,
    },
    4: {
      id: 4,
      branchName: "Cochin Logistics Hub",
      location: "Cochin, Kerala",
      branchType: "Terminal",
      managerName: "Sunita Nair",
      contactPhone: "+91-484-1234-5678",
      contactEmail: "<EMAIL>",
      status: "Active",
      lastActivity: "2024-01-06",
      operationalHours: "24/7",
      coordinates: "9.9312, 76.2673",
      description:
        "Modern terminal hub supporting both domestic and international logistics",
      capacity: "40,000 TEU",
      area: "22 hectares",
      established: "2016",
      totalStaff: 210,
      equipmentCount: 16,
    },
    5: {
      id: 5,
      branchName: "Visakhapatnam Office",
      location: "Visakhapatnam, Andhra Pradesh",
      branchType: "Office",
      managerName: "Vikram Reddy",
      contactPhone: "+91-891-1234-5678",
      contactEmail: "<EMAIL>",
      status: "Inactive",
      lastActivity: "2024-01-04",
      operationalHours: "09:00 - 17:00",
      coordinates: "17.6868, 83.2185",
      description:
        "Administrative office handling port documentation and client coordination",
      capacity: "N/A",
      area: "5 hectares",
      established: "2020",
      totalStaff: 42,
      equipmentCount: 2,
    },
    6: {
      id: 6,
      branchName: "Kandla Port Yard",
      location: "Kandla, Gujarat",
      branchType: "Yard",
      managerName: "Neha Patel",
      contactPhone: "+91-2836-1234-5678",
      contactEmail: "<EMAIL>",
      status: "Active",
      lastActivity: "2024-01-08",
      operationalHours: "24/7",
      coordinates: "23.0339, 70.2219",
      description:
        "High-capacity yard supporting port-side container storage and equipment staging",
      capacity: "35,000 TEU",
      area: "18 hectares",
      established: "2014",
      totalStaff: 132,
      equipmentCount: 14,
    },
  };

  useEffect(() => {
    // Simulate API call
    const fetchBranch = () => {
      const branchId = parseInt(params.id);
      const branchData = mockBranches[branchId];

      if (branchData) {
        setBranch(branchData);
      }
      setLoading(false);
    };

    fetchBranch();
  }, [params.id]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-lg text-purple-600">Loading branch details...</div>
      </div>
    );
  }

  if (!branch) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-600 mb-4">
          Branch Not Found
        </h2>
        <Button onClick={() => router.back()} variant="outline">
          Go Back
        </Button>
      </div>
    );
  }

  const statusColors = {
    Active: "bg-green-50 text-green-700 border-green-200",
    Inactive: "bg-red-50 text-red-700 border-red-200",
    "Under Maintenance": "bg-yellow-50 text-yellow-700 border-yellow-200",
  };

  const typeColors = {
    Yard: "bg-blue-50 text-blue-700 border-blue-200",
    Terminal: "bg-purple-50 text-purple-700 border-purple-200",
    Depot: "bg-orange-50 text-orange-700 border-orange-200",
    Office: "bg-gray-50 text-gray-700 border-gray-200",
  };

  return (
    <div>
      {/* Header with Back Button */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center gap-2 border-purple-200 text-purple-700 hover:bg-purple-50"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Branches
        </Button>
        <h1 className="text-4xl font-extrabold text-indigo-800 flex items-center gap-3">
          <Building2 className="w-8 h-8" />
          {branch.branchName}
        </h1>
      </div>

      {/* Branch Overview Card */}
      <div className="bg-white rounded-2xl shadow-xl p-6 mb-6 border border-purple-100">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Location */}
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <MapPin className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Location</p>
              <p className="font-semibold text-purple-900">{branch.location}</p>
              {branch.coordinates && (
                <p className="text-xs text-gray-500">{branch.coordinates}</p>
              )}
            </div>
          </div>

          {/* Type & Status */}
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Building2 className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Type & Status</p>
              <div className="flex flex-col gap-1">
                <span
                  className={`inline-block px-2 py-1 rounded-full border text-xs font-bold tracking-wide ${
                    typeColors[branch.branchType] ||
                    "bg-gray-50 text-gray-700 border-gray-200"
                  }`}
                >
                  {branch.branchType}
                </span>
                <span
                  className={`inline-block px-2 py-1 rounded-full border text-xs font-bold tracking-wide ${
                    statusColors[branch.status] ||
                    "bg-gray-50 text-gray-700 border-gray-200"
                  }`}
                >
                  {branch.status}
                </span>
              </div>
            </div>
          </div>

          {/* Manager Contact */}
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <User className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Manager</p>
              <p className="font-semibold text-purple-900">
                {branch.managerName}
              </p>
              <div className="flex items-center gap-1 text-xs text-gray-600">
                <Phone className="w-3 h-3" />
                <span>{branch.contactPhone}</span>
              </div>
              {branch.contactEmail && (
                <div className="flex items-center gap-1 text-xs text-gray-600">
                  <Mail className="w-3 h-3" />
                  <span>{branch.contactEmail}</span>
                </div>
              )}
            </div>
          </div>

          {/* Operational Hours */}
          <div className="flex items-center gap-3">
            <div className="p-2 bg-orange-100 rounded-lg">
              <Clock className="w-5 h-5 text-orange-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Operating Hours</p>
              <p className="font-semibold text-purple-900">
                {branch.operationalHours}
              </p>
              <p className="text-xs text-gray-600">
                Last Activity: {branch.lastActivity}
              </p>
            </div>
          </div>
        </div>

        {/* Description */}
        <div className="mt-6 pt-6 border-t border-purple-100">
          <h3 className="text-lg font-bold text-purple-900 mb-2">
            Description
          </h3>
          <p className="text-gray-700">{branch.description}</p>
        </div>

        {/* Quick Stats */}
        <div className="mt-6 pt-6 border-t border-purple-100">
          <h3 className="text-lg font-bold text-purple-900 mb-4">
            Quick Stats
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-700">
                {branch.capacity}
              </div>
              <div className="text-sm text-gray-600">Capacity</div>
            </div>
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-700">
                {branch.area}
              </div>
              <div className="text-sm text-gray-600">Area</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-700">
                {branch.totalStaff}
              </div>
              <div className="text-sm text-gray-600">Total Staff</div>
            </div>
            <div className="text-center p-3 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-700">
                {branch.equipmentCount}
              </div>
              <div className="text-sm text-gray-600">Equipment</div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabbed Content */}
      <BranchDetailsTabs branch={branch} />
    </div>
  );
};

export default BranchDetailsPage;
