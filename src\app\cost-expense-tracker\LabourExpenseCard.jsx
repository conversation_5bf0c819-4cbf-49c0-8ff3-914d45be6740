import React from "react";
import { TableWrapper, Table, TableRow, TableCell } from "@/components/common/table";

export default function LabourExpenseCard() {
  // Demo data
  const items = [
    { id: 1, type: "Loading", qty: 8, rate: 200, total: 1600 },
    { id: 2, type: "Supervision", qty: 3, rate: 300, total: 900 },
    { id: 3, type: "Unloading", qty: 6, rate: 180, total: 1080 },
    { id: 4, type: "Documentation", qty: 2, rate: 250, total: 500 },
    { id: 5, type: "Security", qty: 4, rate: 150, total: 600 },
  ];
  return (
    <div className="bg-white rounded-xl shadow min-w-[250px]">
      <TableWrapper title="👷 Labour Expenses" headers={["Type", "Qty", "Rate", "Total"]}>
        {items.map(i => (
          <TableRow key={i.id}>
            <TableCell>{i.type}</TableCell>
            <TableCell>{i.qty}</TableCell>
            <TableCell>{i.rate}</TableCell>
            <TableCell>{i.total}</TableCell>
          </TableRow>
        ))}
      </TableWrapper>
    </div>
  );
}
