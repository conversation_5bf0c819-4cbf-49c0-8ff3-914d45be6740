import { Button } from "@/components/ui/button";
import React from "react";
import { TableWrapper, Table, TableRow, TableCell } from "@/components/common/table";

export default function InvoiceAutoPanel() {
  const invoiceItems = [
    { category: "Loading", description: "Cargo loading at Dock A", qty: 120, rate: 50, tax: 18, total: 7080 },
    { category: "Transport", description: "Transport to Yard B", qty: 120, rate: 40, tax: 18, total: 5664 },
    { category: "Storage", description: "Storage for 5 days", qty: 5, rate: 500, tax: 18, total: 2950 },
    { category: "Equipment", description: "Crane usage", qty: 5, rate: 500, tax: 18, total: 2950 },
    { category: "Labour", description: "Labour charges", qty: 8, rate: 200, tax: 18, total: 1888 },
  ];

  const subtotal = invoiceItems.reduce((sum, item) => sum + item.total, 0);
  const gstAmount = subtotal * 0.18; // Assuming 18% GST
  const grandTotal = subtotal + gstAmount;

  return (
    <div className="bg-white rounded-xl shadow-lg p-8 mb-6 border border-gray-200 max-w-4xl mx-auto">
      <div className="flex justify-between items-start mb-8">
        <div>
          <h1 className="text-3xl font-bold text-indigo-800 mb-2">Port Company Pvt. Ltd.</h1>
          <p className="text-sm text-gray-600">GST: 27AAACF1234G1Z5</p>
          <p className="text-sm text-gray-600">Dock Road, Mumbai</p>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-700">Invoice #: <span className="font-semibold">INV-20250627-001</span></p>
          <p className="text-sm text-gray-700">Job Ref: <span className="font-semibold">JOB-001</span></p>
          <p className="text-sm text-gray-700">Date: <span className="font-semibold">2025-06-27</span></p>
          <p className="text-sm text-gray-700">Due: <span className="font-semibold">2025-07-05</span></p>
        </div>
      </div>

      <TableWrapper
        headers={["Category", "Description", "Qty", "Rate", "Tax %", "Total"]}
        className="mb-8"
      >
        {invoiceItems.map((item, index) => (
          <TableRow key={index} variant={index % 2 === 0 ? "even" : "odd"}>
            <TableCell className="text-left font-semibold">{item.category}</TableCell>
            <TableCell className="text-left">{item.description}</TableCell>
            <TableCell>{item.qty}</TableCell>
            <TableCell>{item.rate}</TableCell>
            <TableCell>{item.tax}</TableCell>
            <TableCell className="font-semibold">{item.total}</TableCell>
          </TableRow>
        ))}
      </TableWrapper>

      <div className="flex justify-end mb-8">
        <div className="w-full max-w-xs">
          <div className="flex justify-between py-2 border-b border-gray-200">
            <span className="text-gray-700">Subtotal:</span>
            <span className="font-semibold text-gray-900">{subtotal} ₹</span>
          </div>
          <div className="flex justify-between py-2 border-b border-gray-200">
            <span className="text-gray-700">GST (18%):</span>
            <span className="font-semibold text-gray-900">{gstAmount.toFixed(2)} ₹</span>
          </div>
          <div className="flex justify-between py-2 text-lg font-bold text-indigo-700">
            <span>Grand Total:</span>
            <span>{grandTotal.toFixed(2)} ₹</span>
          </div>
        </div>
      </div>

      <div className="mb-8 text-sm text-gray-600">
        <p className="mb-1">Terms: Payment due within 7 days.</p>
        <p className="mb-1">Bank: HDFC Bank, A/C: **********, IFSC: HDFC0001234</p>
        <p>UPI: portcompany@hdfcbank</p>
      </div>

      <div className="flex justify-between items-end">
        <div className="text-sm text-gray-700">
          Authorised Signature: <span className="border-b border-gray-400 px-10"></span>
        </div>
        <Button>
          Download PDF
        </Button>
      </div>
    </div>
  );
}
