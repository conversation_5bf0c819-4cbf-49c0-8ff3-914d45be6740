import { Button } from "@/components/ui/button";
import React, { useState } from "react";

export default function InvoiceAutoPanel() {
  const [tonnageHandled, setTonnageHandled] = useState("");
  const [portServices, setPortServices] = useState("");
  const [storageDuration, setStorageDuration] = useState("");
  const [equipmentLabour, setEquipmentLabour] = useState("");
  const [gst, setGst] = useState("");
  const [serviceCharges, setServiceCharges] = useState("");
  const [readOnly, setReadOnly] = useState(true);

  const handleAutoFill = () => {
    setTonnageHandled("150");
    setPortServices("Unloading, Haulage, Warehousing");
    setStorageDuration("7 days (2025-07-01 to 2025-07-08)");
    setEquipmentLabour("Forklift: 10h, Labour: 16h");
    setGst("20");
    setServiceCharges("750");
    setReadOnly(true); // Set to read-only after auto-fill
  };

  const handleOverrideRates = () => {
    setReadOnly(!readOnly);
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 mb-6 border border-gray-200">
      <h2 className="text-2xl font-bold text-gray-800 mb-5 flex items-center gap-2 pb-3 border-b border-gray-200">
        🧾 Invoice Auto-Generation
      </h2>
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
        <div className="p-4 bg-gray-50 rounded-lg border border-gray-100">
          <label className="block text-sm font-semibold text-gray-700 mb-1">Tonnage Handled</label>
          <input
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition duration-200 ease-in-out bg-white text-gray-800"
            value={tonnageHandled}
            onChange={(e) => setTonnageHandled(e.target.value)}
            readOnly={readOnly}
            placeholder="eg. 120"
          />
        </div>
        <div className="p-4 bg-gray-50 rounded-lg border border-gray-100">
          <label className="block text-sm font-semibold text-gray-700 mb-1">Port Services</label>
          <input
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition duration-200 ease-in-out bg-white text-gray-800"
            value={portServices}
            onChange={(e) => setPortServices(e.target.value)}
            readOnly={readOnly}
            placeholder="eg. Loading, Transport, Storage"
          />
        </div>
        <div className="p-4 bg-gray-50 rounded-lg border border-gray-100">
          <label className="block text-sm font-semibold text-gray-700 mb-1">Storage Duration</label>
          <input
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition duration-200 ease-in-out bg-white text-gray-800"
            value={storageDuration}
            onChange={(e) => setStorageDuration(e.target.value)}
            readOnly={readOnly}
            placeholder="eg. 5 days (2025-06-20 to 2025-06-25)"
          />
        </div>
        <div className="p-4 bg-gray-50 rounded-lg border border-gray-100">
          <label className="block text-sm font-semibold text-gray-700 mb-1">Equipment & Labour</label>
          <input
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition duration-200 ease-in-out bg-white text-gray-800"
            value={equipmentLabour}
            onChange={(e) => setEquipmentLabour(e.target.value)}
            readOnly={readOnly}
            placeholder="eg. Crane: 5h, Labour: 8h"
          />
        </div>
        <div className="p-4 bg-gray-50 rounded-lg border border-gray-100">
          <label className="block text-sm font-semibold text-gray-700 mb-1">GST (%)</label>
          <input
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition duration-200 ease-in-out bg-white text-gray-800"
            value={gst}
            onChange={(e) => setGst(e.target.value)}
            readOnly={readOnly}
            placeholder="eg. 18"
          />
        </div>
        <div className="p-4 bg-gray-50 rounded-lg border border-gray-100">
          <label className="block text-sm font-semibold text-gray-700 mb-1">Service Charges</label>
          <input
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition duration-200 ease-in-out bg-white text-gray-800"
            value={serviceCharges}
            onChange={(e) => setServiceCharges(e.target.value)}
            readOnly={readOnly}
            placeholder="eg. 500"
          />
        </div>
      </div>
      <div className="flex justify-end gap-3">
        <Button
          className="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
          onClick={handleAutoFill}
        >
          Auto-Fill
        </Button>
        <Button
          className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2"
          onClick={handleOverrideRates}
        >
          Override Rates
        </Button>
      </div>
    </div>
  );
}
