import React from "react";
import {
  TableWrapper,
  Table,
  TableRow,
  TableCell,
} from "@/components/common/table";
import { cn } from "@/lib/utils";
import { useSearchParams } from "next/navigation";

export default function WharfGodownSection() {
  // Demo data
  const equipment = [
    {
      id: 1,
      name: "Crane X1",
      type: "Internal",
      hours: 5,
      capacity: "20T",
      tonnage: 100,
      vesselName: "MV Ever Given",
      customerName: "ABC Ltd.",
      branchName: "Mumbai",
    },
    {
      id: 2,
      name: "Loader L2",
      type: "External",
      hours: 3,
      capacity: "10T",
      tonnage: 40,
      vesselName: "MV Maersk",
      customerName: "XYZ Exports",
      branchName: "Kandla",
    },
    {
      id: 3,
      name: "Conveyor C3",
      type: "Internal",
      hours: 7,
      capacity: "5T",
      tonnage: 30,
      vesselName: "MV MSC",
      customerName: "PQR Inc.",
      branchName: "Kolkata",
    },
  ];

  const labour = [
    {
      id: 1,
      name: "<PERSON><PERSON>",
      role: "Operator",
      task: "Loading",
      hours: 4,
      vesselName: "MV Ever Given",
      customerName: "ABC Ltd.",
      branchName: "Mumbai",
    },
    {
      id: 2,
      name: "<PERSON><PERSON>",
      role: "Supervisor",
      task: "Supervision",
      hours: 5,
      vesselName: "MV Maersk",
      customerName: "XYZ Exports",
      branchName: "Chennai",
    },
    {
      id: 3,
      name: "C. Patel",
      role: "Loader",
      task: "Unloading",
      hours: 3,
      vesselName: "MV MSC",
      customerName: "PQR Inc.",
      branchName: "Kandla",
    },
  ];
  const roleColors = {
    Operator: "bg-blue-100 text-blue-800",
    Supervisor: "bg-green-100 text-green-800",
    Loader: "bg-yellow-100 text-yellow-800",
  };
  const searchParams = useSearchParams();
  const branch = searchParams.get("branch");
  const filteredEquipments = branch
    ? equipment.filter((equip) => equip.branchName === branch)
    : equipment;

  const filteredLabour=branch
    ? labour.filter((lab) => lab.branchName === branch)
    : labour;

  return (
    <div className="space-y-6">
      <TableWrapper
        title="🦾 Heavy Equipment Details"
        headers={[
          "Name",
          "Type",
          "Hours Used",
          "Capacity",
          "Branch",
          "Customer",
          "Vessel",
        ]}
      >
        {filteredEquipments.map((eq, idx) => (
          <TableRow key={eq.id} variant={idx % 2 === 0 ? "even" : "odd"}>
            <TableCell>{eq.name}</TableCell>
            <TableCell>
              <span
                className={cn(
                  "px-2 py-1 rounded text-xs font-medium",
                  eq.type === "Internal"
                    ? "bg-blue-100 text-blue-700"
                    : "bg-gray-100 text-gray-700"
                )}
              >
                {eq.type}
              </span>
            </TableCell>
            <TableCell>{eq.hours}h</TableCell>
            <TableCell>{eq.capacity}</TableCell>
            <TableCell>{eq.branchName}</TableCell>
            <TableCell>{eq.customerName}</TableCell>
            <TableCell>{eq.vesselName}</TableCell>
          </TableRow>
        ))}
      </TableWrapper>

      <TableWrapper
        title="👷‍♂️ Labour Usage Logs"
        headers={[
          "Name",
          "Role",
          "Task",
          "Time Spent",
          "Branch",
          "Customer",
          "Vessel",
        ]}
      >
        {filteredLabour.map((l, idx) => (
          <TableRow key={l.id} variant={idx % 2 === 0 ? "even" : "odd"}>
            <TableCell>{l.name}</TableCell>
            <TableCell>
              <span
                className={cn(
                  "px-2 py-1 rounded text-xs font-medium",
                  roleColors[l.role]
                )}
              >
                {l.role}
              </span>
            </TableCell>
            <TableCell>{l.task}</TableCell>
            <TableCell>{l.hours}h</TableCell>
            <TableCell>{l.branchName}</TableCell>
            <TableCell>{l.customerName}</TableCell>
            <TableCell>{l.vesselName}</TableCell>
          </TableRow>
        ))}
      </TableWrapper>
    </div>
  );
}
