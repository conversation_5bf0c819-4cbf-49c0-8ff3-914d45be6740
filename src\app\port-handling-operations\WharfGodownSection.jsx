import React from "react";
import { TableWrapper, Table, TableRow, TableCell } from "@/components/common/table";
import { cn } from "@/lib/utils";

export default function WharfGodownSection() {
  // Demo data
  const equipment = [
    { id: 1, name: "Crane X1", type: "Internal", hours: 5, capacity: "20T", tonnage: 100 },
    { id: 2, name: "Loader L2", type: "External", hours: 3, capacity: "10T", tonnage: 40 },
    { id: 3, name: "Conveyor C3", type: "Internal", hours: 7, capacity: "5T", tonnage: 30 },
  ];
  const labour = [
    { id: 1, name: "<PERSON><PERSON> <PERSON>", role: "Operator", task: "Loading", hours: 4 },
    { id: 2, name: "<PERSON><PERSON> <PERSON>", role: "Supervisor", task: "Supervision", hours: 5 },
    { id: 3, name: "<PERSON><PERSON> <PERSON>", role: "Loader", task: "Unloading", hours: 3 },
  ];
  const roleColors = { Operator: "bg-blue-100 text-blue-800", Supervisor: "bg-green-100 text-green-800", Loader: "bg-yellow-100 text-yellow-800" };
  return (
    <div className="space-y-6">
      <TableWrapper
        title="🦾 Heavy Equipment Details"
        headers={["Name", "Type", "Hours Used", "Capacity", "Tonnage"]}
      >
        {equipment.map((eq, idx) => (
          <TableRow key={eq.id} variant={idx % 2 === 0 ? "even" : "odd"}>
            <TableCell>{eq.name}</TableCell>
            <TableCell>
              <span
                className={cn(
                  "px-2 py-1 rounded text-xs font-medium",
                  eq.type === "Internal"
                    ? "bg-blue-100 text-blue-700"
                    : "bg-gray-100 text-gray-700"
                )}
              >
                {eq.type}
              </span>
            </TableCell>
            <TableCell>{eq.hours}h</TableCell>
            <TableCell>{eq.capacity}</TableCell>
            <TableCell>{eq.tonnage}T</TableCell>
          </TableRow>
        ))}
      </TableWrapper>

      <TableWrapper
        title="👷‍♂️ Labour Usage Logs"
        headers={["Name", "Role", "Task", "Time Spent"]}
      >
        {labour.map((l, idx) => (
          <TableRow key={l.id} variant={idx % 2 === 0 ? "even" : "odd"}>
            <TableCell>{l.name}</TableCell>
            <TableCell>
              <span
                className={cn(
                  "px-2 py-1 rounded text-xs font-medium",
                  roleColors[l.role]
                )}
              >
                {l.role}
              </span>
            </TableCell>
            <TableCell>{l.task}</TableCell>
            <TableCell>{l.hours}h</TableCell>
          </TableRow>
        ))}
      </TableWrapper>
    </div>
  );
}
