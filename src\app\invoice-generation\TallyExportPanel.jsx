import React from "react";
import { TableWrapper, TableRow, TableCell } from "@/components/common/table";
import { Button } from '@/components/ui/button';

export default function TallyExportPanel() {
  // Demo data
  const exports = [
  { id: 1, party: "ABC Ltd.", amount: 12000, tax: "GST18", ledger: "Sales" },
  { id: 2, party: "XYZ Exports", amount: 6000, tax: "GST18", ledger: "Sales" },
  { id: 3, party: "Port CHA", amount: 2000, tax: "GST18", ledger: "Service Charges" },
  { id: 4, party: "Global Traders", amount: 8500, tax: "GST18", ledger: "Sales" },
  { id: 5, party: "Logistics Hub", amount: 3000, tax: "GST18", ledger: "Handling Fees" },
];


  const headers=["Party", "Amount", "Tax", "Ledger"];
  return (
    <div className="bg-white rounded-xl shadow p-5 mb-6">
      <h2 className="font-bold text-lg mb-2 flex items-center gap-2"> Tally Export Preview</h2>
      <TableWrapper headers={headers} title="📥 Tally Export Preview">
         
            {exports.map(e => (
              <TableRow key={e.id}>
                <TableCell>{e.party}</TableCell>
                <TableCell>{e.amount} ₹</TableCell>
                <TableCell>{e.tax}</TableCell>
                <TableCell>{e.ledger}</TableCell>
              </TableRow>
            ))}
      </TableWrapper>
      <div className="flex justify-end gap-2 mt-4">
        <Button>Export CSV</Button>
        <Button>Export XML</Button>
      </div>
    </div>
  );
}
