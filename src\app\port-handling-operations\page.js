"use client";

import React, { useState } from "react";
import WharfGodownSection from "./WharfGodownSection";
import InternalCartingSection from "./InternalCartingSection";
import StorageManagementSection from "./StorageManagementSection";
import RakeLoadingSection from "./RakeLoadingSection";
import LabourEquipmentSection from "./LabourEquipmentSection";

export default function PortHandlingOperations() {
  const [activeTab, setActiveTab] = useState(0);
  const tabs = [
    {
      key: "wharf",
      label: "Wharf & Godown",
      icon: "🏗️",
      content: <WharfGodownSection />,
    },
    {
      key: "tpt",
      label: "Inter-Carting",
      icon: "🚚",
      content: <InternalCartingSection />,
    },
    {
      key: "storage",
      label: "Storage",
      icon: "🏬",
      content: <StorageManagementSection />,
    },
    {
      key: "rake",
      label: "Rake Loading",
      icon: "🚂",
      content: <RakeLoadingSection />,
    },
    {
      key: "labourEqp",
      label: "Labour & Equipment",
      icon: "👥",
      content: <LabourEquipmentSection />,
    },
  ];

  return (
    <div>
      <h1 className="text-4xl font-extrabold text-indigo-800 mb-6 flex items-center gap-3 border-b-2 border-indigo-200 pb-4">
        ⚓ Port Handling Operations Management
      </h1>
      <div className="mb-8 border-b border-indigo-100 flex flex-wrap justify-center gap-3 p-1 bg-white rounded-lg shadow-inner">
        {tabs.map((tab, idx) => (
          <button
            key={tab.key}
            className={`px-6 py-3 rounded-lg font-bold text-base flex items-center gap-2 transition-all duration-300 ease-in-out focus:outline-none transform hover:scale-105
              ${
                activeTab === idx
                  ? "bg-gradient-to-r from-indigo-600 to-purple-500 text-white shadow-lg border-b-4 border-purple-700"
                  : "bg-white text-gray-700 hover:bg-indigo-50 hover:text-indigo-800"
              }`}
            onClick={() => setActiveTab(idx)}
            type="button"
          >
            <span>{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>
      <div className="bg-transparent">{tabs[activeTab].content}</div>
    </div>
  );
}
