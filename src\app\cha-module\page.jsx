"use client";

import React, { useState } from "react";

import {
  filings,
  duty,
  delivery,
  documents as initialDocuments,
  expenses,
} from "@/data/cha";
import BEFilingTracker from "./BEFilingTracker";
import DutyPaymentStatus from "./DutyPaymentStatus";
import DocumentUploadLog from "./DocumentUploadLog";
import ExpenseTracker from "./ExpenseTracker";
import ReportDownload from "./ReportDownload";

const CHAModule = () => {
  const [documents, setDocuments] = useState(initialDocuments);

  const tabs = [
    {
      label: "BE/SB Filing Tracker",
      content: <BEFilingTracker />,
    },
    {
      label: "Duty/Delivery Payment Status",
      content: <DutyPaymentStatus />,
    },
    {
      label: "Document Upload & Clearance Log",
      content: <DocumentUploadLog />,
    },
    {
      label: "Customs Expense Tracker",
      content: <ExpenseTracker />,
    },
    {
      label: "CHA Report Download",
      content: <ReportDownload />,
    },
  ];

  const [activeTab, setActiveTab] = useState(0);

  return (
    <div>
      <h1 className="text-4xl font-extrabold text-indigo-800 mb-6 flex items-center gap-3 border-b-2 border-indigo-200 pb-4">
        💼 Customs House Agent (CHA) Module
      </h1>
      <div className="mb-6 border-b border-indigo-100 flex gap-2 overflow-x-auto">
        {tabs.map((tab, idx) => (
          <button
            key={tab.label}
            className={`px-4 py-2 rounded-lg font-bold text-base flex items-center gap-2 transition-all duration-300 ease-in-out focus:outline-none
              ${
                activeTab === idx
                  ? "bg-gradient-to-r from-indigo-600 to-purple-500 text-white shadow-lg border-b-4 border-purple-700"
                  : "bg-white text-gray-700 hover:bg-indigo-50 hover:text-indigo-800 hover:shadow-md"
              }`}
            onClick={() => setActiveTab(idx)}
          >
            {tab.label}
          </button>
        ))}
      </div>
      <div className="bg-transparent">{tabs[activeTab].content}</div>
    </div>
  );
};

export default CHAModule;
