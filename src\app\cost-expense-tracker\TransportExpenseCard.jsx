import React from "react";
import { TableWrapper, Table, TableRow, TableCell } from "@/components/common/table";

export default function TransportExpenseCard() {
  // Demo data
  const items = [
    { id: 1, type: "Trailer", route: "Dock A → Yard B", tonnage: 20, rate: 150, total: 3000 },
    { id: 2, type: "Dumper", route: "Yard C → Shed D", tonnage: 15, rate: 120, total: 1800 },
    { id: 3, type: "Tanker", route: "Dock B → Tank Farm", tonnage: 25, rate: 200, total: 5000 },
    { id: 4, type: "Containerized", route: "Yard D → Shed E", tonnage: 18, rate: 170, total: 3060 },
    { id: 5, type: "Flatbed", route: "Dock C → Yard F", tonnage: 22, rate: 160, total: 3520 },
  ];
  return (
    <div className="bg-white rounded-xl shadow p-5 flex-1 min-w-[250px]">
      <TableWrapper title="🚚 Transport" headers={["Type", "Route", "Tonnage", "Rate", "Total"]}>
        {items.map(i => (
          <TableRow key={i.id}>
            <TableCell>{i.type}</TableCell>
            <TableCell>{i.route}</TableCell>
            <TableCell>{i.tonnage}</TableCell>
            <TableCell>{i.rate}</TableCell>
            <TableCell>{i.total}</TableCell>
          </TableRow>
        ))}
      </TableWrapper>
    </div>
  );
}
