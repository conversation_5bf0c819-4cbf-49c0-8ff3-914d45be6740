{"name": "act_ports", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prisma:migrate": "prisma migrate dev", "prisma:generate": "prisma generate", "server": "nodemon server/index"}, "dependencies": {"@prisma/client": "6.10.1", "@shadcn/ui": "^0.0.4", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "2.8.5", "dotenv": "16.5.0", "express": "5.1.0", "jsonwebtoken": "9.0.2", "lucide-react": "^0.523.0", "next": "15.3.4", "prisma": "6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^3.0.2", "tailwind-merge": "^3.3.1", "zod": "3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.4", "nodemon": "3.1.10", "tailwindcss": "^4", "tw-animate-css": "^1.3.4"}}