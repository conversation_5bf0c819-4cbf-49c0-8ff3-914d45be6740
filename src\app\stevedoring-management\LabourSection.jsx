import React from "react";

export default function LabourSection() {
  // Placeholder data for demonstration
  const labourList = [
    {
      id: 1,
      name: "<PERSON>",
      role: "<PERSON><PERSON><PERSON>",
      shift: "Day",
      hours: 6,
      status: "Active",
    },
    {
      id: 2,
      name: "<PERSON>",
      role: "<PERSON>man",
      shift: "Night",
      hours: 2,
      status: "On Break",
    },
    {
      id: 3,
      name: "<PERSON>",
      role: "Operator",
      shift: "Day",
      hours: 5,
      status: "Active",
    },
    {
      id: 4,
      name: "<PERSON>",
      role: "<PERSON><PERSON><PERSON>",
      shift: "Night",
      hours: 3,
      status: "Active",
    },
    {
      id: 5,
      name: "<PERSON>",
      role: "Crane Assistant",
      shift: "Day",
      hours: 4,
      status: "On Break",
    },
    {
      id: 6,
      name: "<PERSON>",
      role: "Forklift Operator",
      shift: "Night",
      hours: 5,
      status: "Active",
    },
  ];

  const statusColors = {
    Active: "bg-green-100 text-green-800",
    "On Break": "bg-yellow-100 text-yellow-800",
    Inactive: "bg-gray-100 text-gray-700",
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 flex-1 min-w-[300px] border border-purple-100">
      <div className="flex items-center mb-4">
        <span className="text-2xl mr-2 text-purple-600">👷‍♂️</span>
        <h2 className="font-bold text-lg text-purple-900">Labour On Site</h2>
      </div>
      <div className="mb-3 text-sm text-purple-700">Personnel Checked In</div>
      <div className="grid grid-cols-1 gap-4">
        {labourList.map((person) => (
          <div
            key={person.id}
            className="flex items-center bg-purple-50 rounded-lg p-4 border border-purple-100"
          >
            <div className="w-10 h-10 rounded-full bg-purple-200 flex items-center justify-center text-xl mr-4 text-purple-800 font-semibold">
              {person.name.charAt(0)}
            </div>
            <div className="flex-1">
              <div className="font-medium text-purple-800">{person.name}</div>
              <div className="text-xs text-purple-600">
                {person.role} | Shift:{" "}
                <span className="font-medium">{person.shift}</span> | Hours:{" "}
                <span className="font-medium">{person.hours}</span>
              </div>
            </div>
            <span
              className={`px-3 py-1 rounded-full text-xs font-medium ${
                statusColors[person.status]
              }`}
            >
              {person.status}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
}
