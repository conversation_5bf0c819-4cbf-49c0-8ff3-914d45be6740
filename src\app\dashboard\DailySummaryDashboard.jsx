import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Tooltip, ResponsiveContainer } from "recharts";

export default function DailySummaryDashboard() {
  // Demo data
  const data = [
    { day: "Mon", vessels: 4, tonnage: 96, jobs: 8 },
    { day: "Tue", vessels: 5, tonnage: 140, jobs: 10 },
    { day: "Wed", vessels: 3, tonnage: 70, jobs: 7 },
    { day: "Thu", vessels: 6, tonnage: 150, jobs: 12 },
    { day: "Fri", vessels: 4, tonnage: 90, jobs: 9 },
  ];
  return (
    <div className="bg-white rounded-2xl shadow-xl p-6 mb-6 transform transition-all duration-300 hover:scale-[1.01] hover:shadow-2xl">
      <h2 className="font-extrabold text-2xl mb-4 flex items-center gap-3 text-indigo-700 border-b border-indigo-100 pb-3">
        📅 Daily Operational Summary
      </h2>
      <div className="h-64 w-full">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }} barCategoryGap="20%">
            <XAxis dataKey="day" axisLine={false} tickLine={false} className="text-sm text-gray-600" />
            <YAxis axisLine={false} tickLine={false} className="text-sm text-gray-600" />
            <Tooltip cursor={{ fill: 'transparent' }} contentStyle={{ borderRadius: '8px', border: 'none', boxShadow: '0 4px 12px rgba(0,0,0,0.1)' }} />
            <Bar dataKey="vessels" fill="#6366f1" barSize={30} radius={[10, 10, 0, 0]} name="Vessels" />
            <Bar dataKey="tonnage" fill="#34d399" barSize={30} radius={[10, 10, 0, 0]} name="Tonnage" />
            <Bar dataKey="jobs" fill="#facc15" barSize={30} radius={[10, 10, 0, 0]} name="Jobs" />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
