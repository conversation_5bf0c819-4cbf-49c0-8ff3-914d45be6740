import React from "react";
import { TableWrapper, TableRow, TableCell } from "@/components/common/table";

const ReportDownload = () => {
  const handleDownload = () => {
    // Demo: show toast/snackbar
    alert("Report ready! (Download simulated)");
  };

  const handleView = (reportId) => {
    alert(`Viewing report ${reportId}`);
  };

  const handleDownloadReport = (reportId) => {
    alert(`Downloading report ${reportId}`);
  };

  // Dummy data for CHA Reports
  const chaReports = [
    {
      id: 1,
      reportName: "Import Clearance Report - Jan 2023",
      dateGenerated: "2023-01-31",
      status: "Completed",
      vessel: "MV Ever Given",
      shipment: "SHP-001",
    },
    {
      id: 2,
      reportName: "Export Documentation - Feb 2023",
      dateGenerated: "2023-02-28",
      status: "Pending",
      vessel: "MV Maersk Line",
      shipment: "SHP-002",
    },
    {
      id: 3,
      reportName: "Duty Payment Summary - Mar 2023",
      dateGenerated: "2023-03-15",
      status: "Completed",
      vessel: "MV MSC Mediterranean",
      shipment: "SHP-003",
    },
    {
      id: 4,
      reportName: "Customs Audit Report - Apr 2023",
      dateGenerated: "2023-04-10",
      status: "In Progress",
      vessel: "MV CMA CGM",
      shipment: "SHP-004",
    },
    {
      id: 5,
      reportName: "Import Clearance Report - May 2023",
      dateGenerated: "2023-05-29",
      status: "Completed",
      vessel: "MV Ocean Queen",
      shipment: "SHP-005",
    },
    {
      id: 6,
      reportName: "Export Documentation - Jun 2023",
      dateGenerated: "2023-06-30",
      status: "Completed",
      vessel: "MV Hanjin Harmony",
      shipment: "SHP-006",
    },
    {
      id: 7,
      reportName: "Duty Payment Summary - Jul 2023",
      dateGenerated: "2023-07-20",
      status: "Pending",
      vessel: "MV Cosco Star",
      shipment: "SHP-007",
    },
    {
      id: 8,
      reportName: "Customs Audit Report - Aug 2023",
      dateGenerated: "2023-08-12",
      status: "In Progress",
      vessel: "MV Evergreen Marine",
      shipment: "SHP-008",
    },
  ];

  const headers = [
    "Report Name",
    "Date Generated",
    "Status",
    "Vessel",
    "Shipment",
    "Actions",
  ];

  return (
    <div className="mb-6">
      <TableWrapper title="CHA Report" headers={headers}>
        {chaReports.length === 0 ? (
          <TableRow>
            <TableCell colSpan={6} className="text-center text-gray-400 py-4">
              No CHA reports available.
            </TableCell>
          </TableRow>
        ) : (
          chaReports.map((report) => (
            <TableRow key={report.id}>
              <TableCell>{report.reportName}</TableCell>
              <TableCell>{report.dateGenerated}</TableCell>
              <TableCell>{report.status}</TableCell>
              <TableCell>{report.vessel}</TableCell>
              <TableCell>{report.shipment}</TableCell>
              <TableCell>
                <div className="flex justify-center items-center space-x-2">
                  <button
                    onClick={() => handleDownloadReport(report.id)}
                    className="text-green-600 hover:text-green-800 focus:outline-none"
                    title="Download Report"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              </TableCell>
            </TableRow>
          ))
        )}
      </TableWrapper>
    </div>
  );
};

export default ReportDownload;
