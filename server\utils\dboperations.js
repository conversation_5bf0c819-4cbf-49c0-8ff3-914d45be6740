// dboperations.js - Generic CRUD operations for Postgres using Prisma
import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();

// Create a new user
export async function createUser(data) {
  return prisma.user.create({ data });
}

// Get all users
export async function getUsers() {
  return prisma.user.findMany();
}

// Get a user by ID
export async function getUserById(id) {
  return prisma.user.findUnique({ where: { id } });
}

// Update a user by ID
export async function updateUser(id, data) {
  return prisma.user.update({ where: { id }, data });
}

// Delete (or deactivate) a user by ID
export async function deleteUser(id) {
  // Soft delete: set active to false
  return prisma.user.update({ where: { id }, data: { active: false } });
}
