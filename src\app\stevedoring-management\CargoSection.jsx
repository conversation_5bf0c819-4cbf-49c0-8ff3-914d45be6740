import React from "react";

export default function CargoSection() {
  const cargoList = [
    {
      id: 1,
      type: "Container",
      quantity: 120,
      location: "Dock A",
      status: "Stored",
    },
    {
      id: 2,
      type: "Bulk",
      quantity: 80,
      location: "Warehouse 2",
      status: "Stored",
    },
    {
      id: 3,
      type: "Liquid",
      quantity: 50,
      location: "Tank 1",
      status: "In Transit",
    },
    {
      id: 4,
      type: "Container",
      quantity: 95,
      location: "Dock B",
      status: "Dispatched",
    },
    {
      id: 5,
      type: "Reefer",
      quantity: 30,
      location: "Cold Storage",
      status: "Stored",
    },
    {
      id: 6,
      type: "Bulk",
      quantity: 150,
      location: "Warehouse 1",
      status: "In Transit",
    },
    {
      id: 7,
      type: "Liquid",
      quantity: 70,
      location: "Tank 2",
      status: "Stored",
    },
    {
      id: 8,
      type: "Hazardous",
      quantity: 10,
      location: "Isolation Area",
      status: "Quarantined",
    },
  ];

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 flex-1 min-w-[300px] border border-purple-100">
      <div className="flex items-center mb-4">
        <span className="text-2xl mr-2 text-purple-600">📦</span>
        <h2 className="font-bold text-lg text-purple-900">Cargo Operations</h2>
      </div>
      <div className="mb-3 text-sm text-purple-700">Current Cargo Status</div>
      <div className="space-y-3">
        {cargoList.map((cargo) => (
          <div
            key={cargo.id}
            className="bg-purple-50 rounded-lg p-4 flex justify-between items-center border border-purple-100"
          >
            <div>
              <div className="font-semibold text-purple-800">{cargo.type}</div>
              <div className="text-xs text-purple-600">
                <span className="font-medium">{cargo.quantity}</span> units |
                <span className="font-medium ml-1">{cargo.location}</span>
              </div>
            </div>
            {(() => {
              let statusColor = "";
              let textColor = "";
              switch (cargo.status) {
                case "Stored":
                  statusColor = "bg-green-100";
                  textColor = "text-green-700";
                  break;
                case "In Transit":
                  statusColor = "bg-blue-100";
                  textColor = "text-blue-700";
                  break;
                case "Dispatched":
                  statusColor = "bg-indigo-100";
                  textColor = "text-indigo-700";
                  break;
                case "Quarantined":
                  statusColor = "bg-red-100";
                  textColor = "text-red-700";
                  break;
                default:
                  statusColor = "bg-gray-100";
                  textColor = "text-gray-700";
              }
              return (
                <span
                  className={`${statusColor} ${textColor} px-3 py-1 rounded-full text-xs font-medium`}
                >
                  {cargo.status}
                </span>
              );
            })()}
          </div>
        ))}
      </div>
    </div>
  );
}
