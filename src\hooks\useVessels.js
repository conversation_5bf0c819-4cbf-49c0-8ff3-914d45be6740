import { useState, useCallback } from "react";
import { apiGet, apiPost, apiPut, apiDelete } from "@/utils/api";

const VESSELS_API = "/api/vessels";

export const useVessels = () => {
  const [vessels, setVessels] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Fetch all vessels
  const fetchVessels = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await apiGet(VESSELS_API);
      setVessels(data);
    } catch (err) {
      setError(err.message || "Failed to fetch vessels");
    } finally {
      setLoading(false);
    }
  }, []);

  // Add a vessel
  const addVessel = useCallback(async (vessel) => {
    setLoading(true);
    setError(null);
    try {
      const newVessel = await apiPost(VESSELS_API, vessel);
      setVessels((prev) => [...prev, newVessel]);
      return newVessel;
    } catch (err) {
      setError(err.message || "Failed to add vessel");
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Update a vessel
  const updateVessel = useCallback(async (id, updates) => {
    setLoading(true);
    setError(null);
    try {
      const updated = await apiPut(`${VESSELS_API}/${id}`, updates);
      setVessels((prev) => prev.map((v) => (v.id === id ? updated : v)));
      return updated;
    } catch (err) {
      setError(err.message || "Failed to update vessel");
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Delete a vessel
  const deleteVessel = useCallback(async (id) => {
    setLoading(true);
    setError(null);
    try {
      await apiDelete(`${VESSELS_API}/${id}`);
      setVessels((prev) => prev.filter((v) => v.id !== id));
    } catch (err) {
      setError(err.message || "Failed to delete vessel");
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    vessels,
    loading,
    error,
    fetchVessels,
    addVessel,
    updateVessel,
    deleteVessel,
    setVessels, // expose for manual override if needed
  };
};
