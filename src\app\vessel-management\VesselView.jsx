import React from "react";
import { Card } from "@/components/ui/card";

const VesselView = ({ vessel, onClose }) => {
  if (!vessel) return null;
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
      <Card className="w-full max-w-lg p-8 relative">
        <button
          className="absolute top-4 right-4 text-blue-400 hover:text-blue-700 text-2xl font-bold px-2 py-1 rounded-full focus:outline-none"
          onClick={onClose}
          aria-label="Close"
        >
          ×
        </button>
        <h2 className="text-2xl font-bold mb-6 text-blue-900">Vessel Details</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(vessel).map(([key, value]) => (
            <div key={key}>
              <div className="text-xs text-blue-500 font-semibold mb-1 capitalize">{key.replace(/([A-Z])/g, ' $1')}</div>
              <div className="text-blue-900 font-medium">{value}</div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};

export default VesselView;
