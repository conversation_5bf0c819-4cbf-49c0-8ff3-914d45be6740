import React, { useState, useMemo } from "react";
import { Table<PERSON>rapper, TableRow, TableCell } from "@/components/common/table";
import { Button } from "@/components/ui/button";
import { Eye, Edit, Power } from "lucide-react";
import { useRouter } from "next/navigation";
import { FormModal } from "@/components/common/form";
import { ConfirmDialog } from "@/components/common/dialog";
import BranchForm from "./BranchForm";

const mockBranches = [
    {
      id: 1,
      branchName: "Mumbai Port Terminal",
      location: "Mumbai, Maharashtra",
      branchType: "Terminal",
      managerName: "<PERSON><PERSON>",
      contactPhone: "+91-22-1234-5678",
      status: "Active",
      lastActivity: "2024-01-08",
      operationalHours: "24/7"
    },
    {
      id: 2,
      branchName: "Chennai Container Yard",
      location: "Chennai, Tamil Nadu",
      branchType: "Yard",
      managerName: "Priya Sharma",
      contactPhone: "+91-44-1234-5678",
      status: "Active",
      lastActivity: "2024-01-07",
      operationalHours: "06:00 - 22:00"
    },
    {
      id: 3,
      branchName: "Kolkata Cargo Depot",
      location: "Kolkata, West Bengal",
      branchType: "Depot",
      managerName: "Amit Singh",
      contactPhone: "+91-33-1234-5678",
      status: "Under Maintenance",
      lastActivity: "2024-01-05",
      operationalHours: "08:00 - 18:00"
    },
    {
      id: 4,
      branchName: "Cochin Logistics Hub",
      location: "Cochin, Kerala",
      branchType: "Terminal",
      managerName: "Sunita Nair",
      contactPhone: "+91-484-1234-5678",
      status: "Active",
      lastActivity: "2024-01-06",
      operationalHours: "24/7"
    },
    {
      id: 5,
      branchName: "Visakhapatnam Office",
      location: "Visakhapatnam, Andhra Pradesh",
      branchType: "Office",
      managerName: "Vikram Reddy",
      contactPhone: "+91-891-1234-5678",
      status: "Inactive",
      lastActivity: "2024-01-04",
      operationalHours: "09:00 - 17:00"
    },
    {
      id: 6,
      branchName: "Kandla Port Yard",
      location: "Kandla, Gujarat",
      branchType: "Yard",
      managerName: "Neha Patel",
      contactPhone: "+91-2836-1234-5678",
      status: "Active",
      lastActivity: "2024-01-08",
      operationalHours: "24/7"
    }
  ];

const BranchTable = ({ searchTerm, filters }) => {
  const router = useRouter();
  const [branches, setBranches]=useState(mockBranches)
  const [editBranch, setEditBranch] = useState(null);
  const [disableBranch, setDisableBranch] = useState(null);

  // Mock data for demonstration
  

  const allBranches = branches;

  const headers = [
    "Branch Name",
    "Location",
    "Branch Type",
    "Manager/Contact",
    "Operational Status",
    "Last Activity",
    "Actions"
  ];

  // Status color mapping
  const statusColors = {
    "Active": "bg-green-50 text-green-700 border-green-200",
    "Inactive": "bg-red-50 text-red-700 border-red-200",
    "Under Maintenance": "bg-yellow-50 text-yellow-700 border-yellow-200"
  };

  // Branch type color mapping
  const typeColors = {
    "Yard": "bg-blue-50 text-blue-700 border-blue-200",
    "Terminal": "bg-purple-50 text-purple-700 border-purple-200",
    "Depot": "bg-orange-50 text-orange-700 border-orange-200",
    "Office": "bg-gray-50 text-gray-700 border-gray-200"
  };

  // Filter and search branches
  const filteredBranches = useMemo(() => {
    let filtered = allBranches;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(branch =>
        branch.branchName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        branch.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
        branch.managerName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply branch type filter
    if (filters.branchType) {
      filtered = filtered.filter(branch => branch.branchType === filters.branchType);
    }

    // Apply status filter
    if (filters.status) {
      filtered = filtered.filter(branch => branch.status === filters.status);
    }

    return filtered;
  }, [allBranches, searchTerm, filters]);

  const handleView = (branch) => {
    router.push(`/site-operations/${branch.id}`);
  };

  const handleEdit = (updatedBranch) => {
    setBranches(prev => prev.map(branch => 
      branch.id === updatedBranch.id ? updatedBranch : branch
    ));
    setEditBranch(null);
  };

  const handleDisable = (branchToDisable) => {
    setBranches(prev => prev.map(branch => 
      branch.id === branchToDisable.id 
        ? { ...branch, status: branch.status === "Active" ? "Inactive" : "Active" }
        : branch
    ));
    setDisableBranch(null);
  };

  return (
    <>
      <TableWrapper title="Branch Network Overview" headers={headers}>
        {filteredBranches.length === 0 ? (
          <TableRow>
            <TableCell colSpan={7} className="text-center text-purple-400 py-8">
              {searchTerm || filters.branchType || filters.status 
                ? "No branches found matching your criteria." 
                : "No branches found."}
            </TableCell>
          </TableRow>
        ) : (
          filteredBranches.map((branch, idx) => (
            <TableRow
              key={branch.id}
              variant={idx % 2 === 0 ? "even" : "odd"}
            >
              <TableCell className="font-bold text-purple-900">
                {branch.branchName}
              </TableCell>
              <TableCell className="text-purple-700">
                {branch.location}
              </TableCell>
              <TableCell>
                <span
                  className={`inline-block px-3 py-1 rounded-full border text-xs font-bold tracking-wide shadow transition-colors select-none ${
                    typeColors[branch.branchType] || "bg-gray-50 text-gray-700 border-gray-200"
                  }`}
                >
                  {branch.branchType}
                </span>
              </TableCell>
              <TableCell className="text-purple-700">
                <div>
                  <div className="font-semibold">{branch.managerName}</div>
                  <div className="text-sm text-gray-600">{branch.contactPhone}</div>
                </div>
              </TableCell>
              <TableCell>
                <span
                  className={`inline-block px-3 py-1 rounded-full border text-xs font-bold tracking-wide shadow transition-colors select-none ${
                    statusColors[branch.status] || "bg-gray-50 text-gray-700 border-gray-200"
                  }`}
                >
                  {branch.status}
                </span>
              </TableCell>
              <TableCell className="text-purple-700">
                {branch.lastActivity}
              </TableCell>
              <TableCell>
                <div className="flex gap-2 justify-center">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleView(branch)}
                    className="text-purple-500 hover:bg-purple-100 hover:text-purple-700 rounded-full p-2"
                    title="View Details"
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setEditBranch(branch)}
                    className="text-green-500 hover:bg-green-100 hover:text-green-700 rounded-full p-2"
                    title="Edit"
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setDisableBranch(branch)}
                    className={`rounded-full p-2 ${
                      branch.status === "Active" 
                        ? "text-red-500 hover:bg-red-100 hover:text-red-700" 
                        : "text-green-500 hover:bg-green-100 hover:text-green-700"
                    }`}
                    title={branch.status === "Active" ? "Disable" : "Enable"}
                  >
                    <Power className="w-4 h-4" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))
        )}
      </TableWrapper>

      {/* Edit Modal */}
      {editBranch && (
        <FormModal
          open={!!editBranch}
          onClose={() => setEditBranch(null)}
          title="Edit Branch"
        >
          <BranchForm
            onAdd={handleEdit}
            initialData={editBranch}
            isEdit
          />
        </FormModal>
      )}

      {/* Disable/Enable Dialog */}
      {disableBranch && (
        <ConfirmDialog
          open={!!disableBranch}
          title={`${disableBranch.status === "Active" ? "Disable" : "Enable"} Branch`}
          message={`Are you sure you want to ${disableBranch.status === "Active" ? "disable" : "enable"} "${disableBranch.branchName}"?`}
          onCancel={() => setDisableBranch(null)}
          onConfirm={() => handleDisable(disableBranch)}
        />
      )}
    </>
  );
};

export default BranchTable;
