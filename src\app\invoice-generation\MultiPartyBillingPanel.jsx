import React from 'react';

export default function MultiPartyBillingPanel() {
  // Demo data
  const parties = [
    { id: 1, name: "ABC Ltd.", type: "Importer", percent: 60, amount: 12000 },
    { id: 2, name: "XYZ Exports", type: "Exporter", percent: 30, amount: 6000 },
    { id: 3, name: "Port CHA", type: "CHA", percent: 10, amount: 2000 },
    { id: 4, name: "Global Logistics", type: "Carrier", percent: 25, amount: 5000 },
    { id: 5, name: "Ocean Freight Inc.", type: "Freight Forwarder", percent: 15, amount: 3000 },
    { id: 6, name: "Customs Brokerage", type: "Customs Broker", percent: 5, amount: 1000 },
  ];
  return (
    <div className="bg-white rounded-xl shadow p-5 mb-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        {parties.map(p => (
         <div
  key={p.id}
  className="flex-1 bg-gradient-to-br from-purple-50 to-indigo-50 rounded-xl shadow-md p-5 text-center border border-purple-100 transform hover:scale-105 transition-all duration-300 ease-in-out cursor-pointer"
>
            <div className="font-extrabold text-indigo-800 text-xl mb-1">{p.name}</div>
            <div className="text-xs font-semibold text-purple-700 bg-purple-200 px-2 py-1 rounded-full inline-block mb-2">{p.type}</div>
            <div className="text-3xl font-extrabold text-indigo-700 mt-3 mb-1">{p.percent}%</div>
            <div className="text-base text-gray-800">{p.amount} ₹</div>
          </div>
        ))}
      </div>

      
    </div>
  );
}
