"use client"

import { documents } from "@/data/cha";
import React, { useRef } from "react";
import { TableWrapper, Table, TableRow, TableCell } from "@/components/common/table";

const DocumentUploadLog = () => {
  const fileInput = useRef();

  const handleUpload = (files) => {
    // Demo: just append file names
    const newDocs = Array.from(files).map((f) => ({
      name: f.name,
      uploadedAt: new Date().toISOString().slice(0, 16).replace("T", " "),
      status: "Pending",
      clearedBy: "",
      remarks: "-",
    }));
    console.log(newDocs);
  };

  const handleFileChange = (e) => {
    // Assuming onUpload is passed as a prop or defined elsewhere if needed
    // For now, directly call handleUpload
    if (e.target.files) handleUpload(e.target.files);
  };

  return (
    <div className="mb-6">
      <div className="flex items-center gap-4 mb-4 px-6 pt-4">
        <input
          type="file"
          multiple
          ref={fileInput}
          onChange={handleFileChange}
          className="hidden"
        />
        <button
          className="px-4 py-2 bg-purple-700 text-white rounded shadow hover:bg-purple-800 transition-colors duration-200"
          onClick={() => fileInput.current && fileInput.current.click()}
        >
          Upload Documents
        </button>
        <span className="text-purple-400">or drag & drop</span>
      </div>
      <TableWrapper title="Document Upload & Clearance Log" headers={["Document", "Uploaded", "Status", "Cleared By", "Remarks"]}>
        {
          documents.length === 0 ? (
            <TableRow>
              <TableCell colSpan={5} className="text-center text-purple-400 py-4">
                No documents uploaded.
              </TableCell>
            </TableRow>
          ) : (
            documents.map((doc, i) => (
              <TableRow key={i}>
                <TableCell>{doc.name}</TableCell>
                <TableCell>{doc.uploadedAt}</TableCell>
                <TableCell>
                  <span
                    className={`inline-block px-3 py-1 rounded-full text-xs font-semibold shadow ${
                      doc.status === "Cleared"
                        ? "bg-green-100 text-green-700"
                        : "bg-yellow-100 text-yellow-700"
                    }`}
                  >
                    {doc.status}
                  </span>
                </TableCell>
                <TableCell>{doc.clearedBy || "-"}</TableCell>
                <TableCell>{doc.remarks || "-"}</TableCell>
              </TableRow>
            ))
          )
        }
      </TableWrapper>
    </div>
  );
};

export default DocumentUploadLog;
