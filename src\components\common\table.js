import * as React from "react";
import { cn } from "@/lib/utils";

export function TableWrapper({ title, headers = [], children, className }) {
  return (
    <div
      className={cn(
        "rounded-xl border border-purple-200 bg-gradient-to-br from-white/70 via-white/60 to-purple-50/40 shadow-xl backdrop-blur-lg overflow-hidden",
        className
      )}
    >
      {title && (
        <div className="px-6 pt-5 pb-3 text-xl font-bold text-purple-900 bg-purple-50 border-b border-purple-100 tracking-wide">
          {title}
        </div>
      )}
      <Table headers={headers}>{children}</Table>
    </div>
  );
}

export function Table({ headers = [], children, className }) {
  return (
    <div className={cn("w-full overflow-x-auto bg-white", className)}>
      <table className="min-w-full divide-y divide-purple-100 rounded-xl overflow-hidden">
        <thead className="bg-purple-100/60 sticky top-0 z-10 shadow-sm">
          <tr>
            {headers.map((header, idx) => (
              <th
                key={idx}
                className="px-5 py-4 text-sm font-bold text-purple-800 uppercase tracking-wide whitespace-nowrap text-center"
              >
                {header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-purple-200">{children}</tbody>
      </table>
    </div>
  );
}

export function TableRow({ children, className, variant }) {
  return (
    <tr
      className={cn(
        "hover:bg-purple-50/60 transition-all duration-150 group",
        variant === "even" && "bg-purple-50/30",
        variant === "odd" && "bg-white/80",
        className
      )}
    >
      {children}
    </tr>
  );
}

export function TableCell({ children, className }) {
  return (
    <td
      className={cn(
        "px-5 py-4 text-sm text-purple-900 text-center whitespace-nowrap font-medium group-hover:bg-purple-50/40 transition-colors duration-150",
        className
      )}
    >
      {children}
    </td>
  );
}

export function TableHeaderCell({ children, className }) {
  return (
    <th
      className={cn(
        "px-5 py-4 text-sm font-bold text-purple-700 uppercase tracking-wider whitespace-nowrap bg-purple-100/80 text-center",
        className
      )}
    >
      {children}
    </th>
  );
}
