import React, { useState, useMemo } from "react";
import { Table<PERSON>rap<PERSON>, TableRow, TableCell } from "@/components/common/table";
import { But<PERSON> } from "@/components/ui/button";
import { Eye, Edit, Trash2 } from "lucide-react";
import ViewModal from "@/components/common/view";
import { FormModal } from "@/components/common/form";
import { ConfirmDialog } from "@/components/common/dialog";
import ShipmentForm from "./ShipmentForm";

const RecentShipmentsTable = ({ searchTerm, shipments, setShipments }) => {
  const [viewShipment, setViewShipment] = useState(null);
  const [editShipment, setEditShipment] = useState(null);
  const [deleteShipment, setDeleteShipment] = useState(null);

  const headers = [
    "Shipment ID",
    "Cargo Type",
    "Origin",
    "Destination",
    "Vessel Name",
    "Customer Name",
    "Status",
    "ETA",
    "Actions",
  ];

  // Status color mapping
  const statusColors = {
    Booked: "bg-blue-50 text-blue-700 border-blue-200",
    "In Transit": "bg-purple-50 text-purple-700 border-purple-200",
    Delivered: "bg-green-50 text-green-700 border-green-200",
    Delayed: "bg-red-50 text-red-700 border-red-200",
  };

  // Filter shipments based on search term
  const filteredShipments = useMemo(() => {
    if (!searchTerm) return shipments;

    return shipments.filter((shipment) =>
      Object.values(shipment).some((value) =>
        value.toString().toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  }, [searchTerm, shipments]);

  const handleView = (shipment) => {
    setViewShipment(shipment);
  };

  const handleEdit = (updatedShipment) => {
    setShipments((prev) =>
      prev.map((shipment) =>
        shipment.id === updatedShipment.id ? updatedShipment : shipment
      )
    );
    setEditShipment(null);
  };

  const handleDelete = (shipmentToDelete) => {
    setShipments((prev) =>
      prev.filter((shipment) => shipment.id !== shipmentToDelete.id)
    );
    setDeleteShipment(null);
  };

  return (
    <>
      <TableWrapper title="Recent Shipments" headers={headers}>
        {filteredShipments.length === 0 ? (
          <TableRow>
            <TableCell colSpan={9} className="text-center text-purple-400 py-8">
              {searchTerm
                ? "No shipments found matching your search."
                : "No shipments found."}
            </TableCell>
          </TableRow>
        ) : (
          filteredShipments.map((shipment, idx) => (
            <TableRow
              key={shipment.shipmentId}
              variant={idx % 2 === 0 ? "even" : "odd"}
            >
              <TableCell className="font-bold text-purple-900">
                {shipment.shipmentId}
              </TableCell>
              <TableCell className="text-purple-700">
                {shipment.cargoType}
              </TableCell>
              <TableCell className="text-purple-700">
                {shipment.origin}
              </TableCell>
              <TableCell className="text-purple-700">
                {shipment.destination}
              </TableCell>
              <TableCell className="text-purple-700">
                {shipment.vesselName}
              </TableCell>
              <TableCell className="text-purple-700">
                {shipment.customerName}
              </TableCell>
              <TableCell>
                <span
                  className={`inline-block px-4 py-1 rounded-full border text-xs font-bold tracking-wide shadow transition-colors select-none ${
                    statusColors[shipment.status] ||
                    "bg-purple-50 text-purple-400 border-purple-100"
                  }`}
                >
                  {shipment.status}
                </span>
              </TableCell>
              <TableCell className="text-purple-700">{shipment.eta}</TableCell>
              <TableCell>
                <div className="flex gap-2 justify-center">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleView(shipment)}
                    className="text-purple-500 hover:bg-purple-100 hover:text-purple-700 rounded-full p-2"
                    title="View"
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setEditShipment(shipment)}
                    className="text-green-500 hover:bg-green-100 hover:text-green-700 rounded-full p-2"
                    title="Edit"
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setDeleteShipment(shipment)}
                    className="text-red-500 hover:bg-red-100 hover:text-red-700 rounded-full p-2"
                    title="Delete"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))
        )}
      </TableWrapper>

      {/* View Modal */}
      {viewShipment && (
        <ViewModal
          object={viewShipment}
          onClose={() => setViewShipment(null)}
          title="Shipment Details"
        />
      )}

      {/* Edit Modal */}
      {editShipment && (
        <FormModal
          open={!!editShipment}
          onClose={() => setEditShipment(null)}
          title="Edit Shipment"
        >
          <ShipmentForm onAdd={handleEdit} initialData={editShipment} isEdit />
        </FormModal>
      )}

      {/* Delete Dialog */}
      {deleteShipment && (
        <ConfirmDialog
          open={!!deleteShipment}
          title="Delete Shipment"
          message={`Are you sure you want to delete shipment "${deleteShipment.shipmentId}"? This action cannot be undone.`}
          onCancel={() => setDeleteShipment(null)}
          onConfirm={() => handleDelete(deleteShipment)}
        />
      )}
    </>
  );
};

export default RecentShipmentsTable;
