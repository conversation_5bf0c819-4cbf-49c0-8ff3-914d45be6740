import React, { useState, useMemo } from "react";
import { TableWrapper, TableRow, TableCell } from "@/components/common/table";
import { Button } from "@/components/ui/button";
import { Eye, Edit, Trash2 } from "lucide-react";

const RecentShipmentsTable = ({ searchTerm }) => {
  // Mock data for shipments
  const shipmentsData = [
    {
      shipmentId: "FF-2024-001",
      cargoType: "Electronics",
      origin: "Shanghai, China",
      destination: "Los Angeles, USA",
      vesselName: "MSC Gulsun",
      customerName: "TechCorp Inc.",
      status: "In Transit",
      eta: "2024-01-15"
    },
    {
      shipmentId: "FF-2024-002",
      cargoType: "Textiles",
      origin: "Mumbai, India",
      destination: "Hamburg, Germany",
      vesselName: "Ever Given",
      customerName: "Fashion Ltd.",
      status: "Booked",
      eta: "2024-01-20"
    },
    {
      shipmentId: "FF-2024-003",
      cargoType: "Machinery",
      origin: "Busan, South Korea",
      destination: "Rotterdam, Netherlands",
      vesselName: "OOCL Hong Kong",
      customerName: "Industrial Co.",
      status: "Delivered",
      eta: "2024-01-10"
    },
    {
      shipmentId: "FF-2024-004",
      cargoType: "Food Products",
      origin: "Santos, Brazil",
      destination: "New York, USA",
      vesselName: "CMA CGM Marco Polo",
      customerName: "Food Distributors",
      status: "Delayed",
      eta: "2024-01-18"
    },
    {
      shipmentId: "FF-2024-005",
      cargoType: "Automotive Parts",
      origin: "Yokohama, Japan",
      destination: "Long Beach, USA",
      vesselName: "Maersk Madrid",
      customerName: "Auto Parts Inc.",
      status: "In Transit",
      eta: "2024-01-22"
    },
    {
      shipmentId: "FF-2024-006",
      cargoType: "Chemicals",
      origin: "Antwerp, Belgium",
      destination: "Singapore",
      vesselName: "Hapag-Lloyd Berlin",
      customerName: "ChemCorp",
      status: "Booked",
      eta: "2024-01-25"
    }
  ];

  const headers = [
    "Shipment ID",
    "Cargo Type", 
    "Origin",
    "Destination",
    "Vessel Name",
    "Customer Name",
    "Status",
    "ETA",
    "Actions"
  ];

  // Status color mapping
  const statusColors = {
    "Booked": "bg-blue-50 text-blue-700 border-blue-200",
    "In Transit": "bg-purple-50 text-purple-700 border-purple-200",
    "Delivered": "bg-green-50 text-green-700 border-green-200",
    "Delayed": "bg-red-50 text-red-700 border-red-200"
  };

  // Filter shipments based on search term
  const filteredShipments = useMemo(() => {
    if (!searchTerm) return shipmentsData;
    
    return shipmentsData.filter(shipment =>
      Object.values(shipment).some(value =>
        value.toString().toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  }, [searchTerm]);

  const handleView = (shipment) => {
    console.log("View shipment:", shipment);
    // TODO: Implement view modal
  };

  const handleEdit = (shipment) => {
    console.log("Edit shipment:", shipment);
    // TODO: Implement edit modal
  };

  const handleDelete = (shipment) => {
    console.log("Delete shipment:", shipment);
    // TODO: Implement delete confirmation
  };

  return (
    <TableWrapper title="Recent Shipments" headers={headers}>
      {filteredShipments.length === 0 ? (
        <TableRow>
          <TableCell colSpan={9} className="text-center text-purple-400 py-8">
            {searchTerm ? "No shipments found matching your search." : "No shipments found."}
          </TableCell>
        </TableRow>
      ) : (
        filteredShipments.map((shipment, idx) => (
          <TableRow
            key={shipment.shipmentId}
            variant={idx % 2 === 0 ? "even" : "odd"}
          >
            <TableCell className="font-bold text-purple-900">
              {shipment.shipmentId}
            </TableCell>
            <TableCell className="text-purple-700">
              {shipment.cargoType}
            </TableCell>
            <TableCell className="text-purple-700">
              {shipment.origin}
            </TableCell>
            <TableCell className="text-purple-700">
              {shipment.destination}
            </TableCell>
            <TableCell className="text-purple-700">
              {shipment.vesselName}
            </TableCell>
            <TableCell className="text-purple-700">
              {shipment.customerName}
            </TableCell>
            <TableCell>
              <span
                className={`inline-block px-4 py-1 rounded-full border text-xs font-bold tracking-wide shadow transition-colors select-none ${
                  statusColors[shipment.status] ||
                  "bg-purple-50 text-purple-400 border-purple-100"
                }`}
              >
                {shipment.status}
              </span>
            </TableCell>
            <TableCell className="text-purple-700">
              {shipment.eta}
            </TableCell>
            <TableCell>
              <div className="flex gap-2 justify-center">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleView(shipment)}
                  className="text-purple-500 hover:bg-purple-100 hover:text-purple-700 rounded-full p-2"
                  title="View"
                >
                  <Eye className="w-4 h-4" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleEdit(shipment)}
                  className="text-green-500 hover:bg-green-100 hover:text-green-700 rounded-full p-2"
                  title="Edit"
                >
                  <Edit className="w-4 h-4" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleDelete(shipment)}
                  className="text-red-500 hover:bg-red-100 hover:text-red-700 rounded-full p-2"
                  title="Delete"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </TableCell>
          </TableRow>
        ))
      )}
    </TableWrapper>
  );
};

export default RecentShipmentsTable;
