import React, { useState } from "react";

const equipmentTypes = {
  Crane: "🦾",
  Forklift: "🚜",
  Trailer: "🚚",
  "Reach Stacker": "🏗️",
};

export default function EquipmentSection() {
  // Placeholder data for demonstration
 const equipmentList = [
  { id: 1, type: "Crane", name: "Crane A1", hours: 5, status: "In Use" },
  { id: 2, type: "Forklift", name: "Forklift F2", hours: 2, status: "Idle" },
  { id: 3, type: "Trailer", name: "Trailer T3", hours: 4, status: "Maintenance" },
  { id: 4, type: "<PERSON>", name: "Crane A2", hours: 3, status: "In Use" },
  { id: 5, type: "Forklift", name: "Forklift F3", hours: 6, status: "In Use" },
  { id: 6, type: "Trailer", name: "Trailer T4", hours: 1, status: "Idle" },
  { id: 7, type: "Crane", name: "Crane A3", hours: 7, status: "Maintenance" },
  { id: 8, type: "Forklift", name: "Forklift F4", hours: 3, status: "In Use" },
  { id: 9, type: "Trailer", name: "Trailer T5", hours: 2, status: "In Use" },
  { id: 10, type: "Crane", name: "Crane A4", hours: 4, status: "Idle" },
  { id: 11, type: "Forklift", name: "Forklift F5", hours: 5, status: "Maintenance" },
  { id: 12, type: "Trailer", name: "Trailer T6", hours: 6, status: "Idle" },

  // New equipment type: Reach Stacker
  { id: 13, type: "Reach Stacker", name: "Stacker R1", hours: 5, status: "In Use" },
  { id: 14, type: "Reach Stacker", name: "Stacker R2", hours: 2, status: "Idle" },
  { id: 15, type: "Reach Stacker", name: "Stacker R3", hours: 6, status: "Maintenance" },
  { id: 16, type: "Reach Stacker", name: "Stacker R4", hours: 3, status: "In Use" },
];

  const statusColors = {
    "In Use": "bg-blue-100 text-blue-800",
    Idle: "bg-gray-100 text-gray-700",
    Maintenance: "bg-orange-100 text-orange-800",
  };

  // Group by type and manage accordion state
  const [openSections, setOpenSections] = useState({});

  const grouped = equipmentList.reduce((acc, eq) => {
    acc[eq.type] = acc[eq.type] || [];
    acc[eq.type].push(eq);
    return acc;
  }, {});

  const toggleSection = (type) => {
    setOpenSections((prev) => ({
      ...prev,
      [type]: !prev[type],
    }));
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 flex-1 min-w-[300px] border border-purple-100">
      <div className="flex items-center mb-4">
        <span className="text-2xl mr-2 text-purple-600">🔧</span>
        <h2 className="font-bold text-lg text-purple-900">Equipment On Site</h2>
      </div>
      <div className="mb-3 text-sm text-purple-700">Current Equipment Usage</div>
      <div className="space-y-4">
        {Object.keys(grouped).map((type) => (
          <div key={type} className="border border-purple-200 rounded-lg">
            <div
              className="flex justify-between items-center p-4 cursor-pointer bg-purple-50 hover:bg-purple-100"
              onClick={() => toggleSection(type)}
            >
              <div className="font-semibold flex items-center text-purple-800">
                <span className="mr-2 text-xl">{equipmentTypes[type]}</span>
                {type}
              </div>
              <div className="flex items-center">
                <span className="text-sm font-medium text-purple-600 mr-3">
                  Count: {grouped[type].length}
                </span>
                <svg
                  className={`w-5 h-5 text-purple-600 transform transition-transform duration-200 ${
                    openSections[type] ? "rotate-180" : ""
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M19 9l-7 7-7-7"
                  ></path>
                </svg>
              </div>
            </div>
            {openSections[type] && (
              <div className="p-4 border-t border-purple-200 bg-white">
                <div className="grid grid-cols-1 gap-3">
                  {grouped[type].map((eq) => (
                    <div
                      key={eq.id}
                      className="bg-white rounded-lg p-3 flex justify-between items-center border border-gray-200 shadow-sm"
                    >
                      <div>
                        <div className="font-medium text-purple-800">{eq.name}</div>
                        <div className="text-xs text-purple-600">
                          Hours: <span className="font-medium">{eq.hours}</span>
                        </div>
                      </div>
                      <span
                        className={`px-3 py-1 rounded-full text-xs font-medium ${statusColors[eq.status]}`}
                      >
                        {eq.status}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
