"use client";

import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";

export default function LoginPage() {
  const router = useRouter();
  const handlePortLogin = (branchName) => {
  router.push(`/port-handling-operations?branch=${branchName}`);
};

  const handleAdminLogin = () => {
    router.push("/dashboard");
  };
  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-purple-100 to-indigo-200">
      <div className="px-10 py-8 mt-6 text-left bg-white shadow-2xl rounded-xl transform transition-all duration-300 hover:scale-[1.01]">
        <h3 className="text-3xl font-extrabold text-center text-indigo-800 mb-8 tracking-tight">
          Login to your account
        </h3>
        <form>
          <div className="mt-4">
            {/* <div className="mb-4"> */}

            <div className="flex flex-col items-center justify-center gap-4 mt-6">
              
              <Button
                type="button"
                onClick={() => handlePortLogin('Mumbai')}
                className="w-full py-3 text-lg font-semibold bg-blue-600 hover:bg-blue-700 rounded-lg shadow-md transition duration-200"
              >
                Login as Mumbai Branch
              </Button>
              <Button
                type="button"
                onClick={() => handlePortLogin('Kandla')}
                className="w-full py-3 text-lg font-semibold bg-green-600 hover:bg-green-700 rounded-lg shadow-md transition duration-200"
              >
                Login as Kandla Port
              </Button>
            
              <Button
                type="button"
                onClick={handleAdminLogin}
                className="w-full py-3 text-lg font-semibold bg-red-600 hover:bg-red-700 rounded-lg shadow-md transition duration-200"
              >
                Login as Admin
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
