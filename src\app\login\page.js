'use client';

import { But<PERSON> } from "@/components/ui/button";

export default function LoginPage() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-purple-100 to-indigo-200">
      <div className="px-10 py-8 mt-6 text-left bg-white shadow-2xl rounded-xl transform transition-all duration-300 hover:scale-[1.01]">
        <h3 className="text-3xl font-extrabold text-center text-indigo-800 mb-8 tracking-tight">Login to your account</h3>
        <form>
          <div className="mt-4">
            {/* <div className="mb-4">
              <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="email">Email</label>
              <input type="email" placeholder="<EMAIL>" className="shadow-sm appearance-none border border-gray-300 rounded-lg w-full py-3 px-4 text-gray-800 leading-tight focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition duration-200" />
            </div>
            <div className="mb-6">
              <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="password">Password</label>
              <input type="password" placeholder="********" className="shadow-sm appearance-none border border-gray-300 rounded-lg w-full py-3 px-4 text-gray-800 mb-3 leading-tight focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition duration-200" />
            </div> */}
            <div className="flex flex-col items-center justify-center gap-4 mt-6">
              {/* <Button type="submit" className="w-full py-3 text-lg font-semibold bg-indigo-600 hover:bg-indigo-700 rounded-lg shadow-md transition duration-200">Login</Button>
              <a href="#" className="inline-block align-baseline font-semibold text-sm text-indigo-500 hover:text-indigo-700 transition duration-200">Forgot password?</a> */}
              <Button type="button" onClick={() => window.location.href = '/dashboard'} className="w-full py-3 text-lg font-semibold bg-purple-600 hover:bg-purple-700 rounded-lg shadow-md transition duration-200">Login as Guest</Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}