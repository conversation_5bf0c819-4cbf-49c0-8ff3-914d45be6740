import React, { useState } from "react";
import { 
  Info, 
  Activity, 
  Package, 
  Users, 
  BarChart3,
  MapPin,
  Truck,
  Clock,
  AlertTriangle,
  CheckCircle
} from "lucide-react";

const BranchDetailsTabs = ({ branch }) => {
  const [activeTab, setActiveTab] = useState(0);

  const tabs = [
    {
      key: "overview",
      label: "Overview",
      icon: Info,
      component: OverviewTab
    },
    {
      key: "operations",
      label: "Operations Log",
      icon: Activity,
      component: OperationsLogTab
    },
    {
      key: "resources",
      label: "Resources",
      icon: Package,
      component: ResourcesTab
    },
    {
      key: "personnel",
      label: "Personnel",
      icon: Users,
      component: PersonnelTab
    },
    {
      key: "reports",
      label: "Reports/Stats",
      icon: BarChart3,
      component: ReportsTab
    }
  ];

  const ActiveComponent = tabs[activeTab].component;

  return (
    <div>
      {/* Tab Navigation */}
      <div className="mb-8 border-b border-purple-100 flex flex-wrap justify-center gap-3 p-1 bg-white rounded-lg shadow-inner">
        {tabs.map((tab, idx) => {
          const IconComponent = tab.icon;
          return (
            <button
              key={tab.key}
              className={`px-6 py-3 rounded-lg font-bold text-base flex items-center gap-2 transition-all duration-300 ease-in-out focus:outline-none transform hover:scale-105
                ${
                  activeTab === idx
                    ? "bg-gradient-to-r from-indigo-600 to-purple-500 text-white shadow-lg border-b-4 border-purple-700"
                    : "bg-white text-gray-700 hover:bg-indigo-50 hover:text-indigo-800"
                }`}
              onClick={() => setActiveTab(idx)}
              type="button"
            >
              <IconComponent className="w-4 h-4" />
              {tab.label}
            </button>
          );
        })}
      </div>

      {/* Tab Content */}
      <div className="bg-transparent">
        <ActiveComponent branch={branch} />
      </div>
    </div>
  );
};

// Overview Tab Component
const OverviewTab = ({ branch }) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Basic Information */}
      <div className="bg-white rounded-2xl shadow-xl p-6 border border-purple-100">
        <h3 className="text-xl font-bold text-purple-900 mb-4 flex items-center gap-2">
          <Info className="w-5 h-5" />
          Basic Information
        </h3>
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-600">Established:</span>
            <span className="font-semibold text-purple-900">{branch.established}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Capacity:</span>
            <span className="font-semibold text-purple-900">{branch.capacity}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Area:</span>
            <span className="font-semibold text-purple-900">{branch.area}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Total Staff:</span>
            <span className="font-semibold text-purple-900">{branch.totalStaff}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Equipment Count:</span>
            <span className="font-semibold text-purple-900">{branch.equipmentCount}</span>
          </div>
        </div>
      </div>

      {/* Map Location */}
      <div className="bg-white rounded-2xl shadow-xl p-6 border border-purple-100">
        <h3 className="text-xl font-bold text-purple-900 mb-4 flex items-center gap-2">
          <MapPin className="w-5 h-5" />
          Location & Map
        </h3>
        <div className="space-y-4">
          <div className="bg-gray-100 rounded-lg p-4 text-center">
            <MapPin className="w-16 h-16 mx-auto mb-2 text-gray-400" />
            <p className="text-gray-600">Interactive map would be displayed here</p>
            <p className="text-sm text-gray-500">Coordinates: {branch.coordinates}</p>
          </div>
          <div className="text-sm text-gray-700">
            <p><strong>Address:</strong> {branch.location}</p>
            <p><strong>Type:</strong> {branch.branchType}</p>
            <p><strong>Operating Hours:</strong> {branch.operationalHours}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

// Operations Log Tab Component
const OperationsLogTab = ({ branch }) => {
  const mockOperations = [
    {
      id: 1,
      timestamp: "2024-01-08 14:30",
      type: "Cargo Handled",
      description: "Container vessel MSC Gulsun - 150 TEU loaded",
      status: "completed",
      operator: "Team A"
    },
    {
      id: 2,
      timestamp: "2024-01-08 12:15",
      type: "Equipment Maintenance",
      description: "Crane #3 scheduled maintenance completed",
      status: "completed",
      operator: "Maintenance Team"
    },
    {
      id: 3,
      timestamp: "2024-01-08 09:45",
      type: "Delay",
      description: "Vessel arrival delayed by 2 hours due to weather",
      status: "resolved",
      operator: "Port Control"
    },
    {
      id: 4,
      timestamp: "2024-01-07 16:20",
      type: "Cargo Handled",
      description: "Break-bulk cargo - 500 MT steel coils unloaded",
      status: "completed",
      operator: "Team B"
    }
  ];

  return (
    <div className="bg-white rounded-2xl shadow-xl p-6 border border-purple-100">
      <h3 className="text-xl font-bold text-purple-900 mb-4 flex items-center gap-2">
        <Activity className="w-5 h-5" />
        Recent Operations Log
      </h3>
      <div className="space-y-4">
        {mockOperations.map((operation) => (
          <div key={operation.id} className="border border-purple-100 rounded-lg p-4 hover:bg-purple-50 transition-colors">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <span className="font-semibold text-purple-900">{operation.type}</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-bold ${
                    operation.status === 'completed' ? 'bg-green-100 text-green-700' :
                    operation.status === 'resolved' ? 'bg-blue-100 text-blue-700' :
                    'bg-yellow-100 text-yellow-700'
                  }`}>
                    {operation.status}
                  </span>
                </div>
                <p className="text-gray-700 mb-2">{operation.description}</p>
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <span className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    {operation.timestamp}
                  </span>
                  <span className="flex items-center gap-1">
                    <Users className="w-3 h-3" />
                    {operation.operator}
                  </span>
                </div>
              </div>
              <div className="ml-4">
                {operation.status === 'completed' ? (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                ) : operation.status === 'resolved' ? (
                  <CheckCircle className="w-5 h-5 text-blue-500" />
                ) : (
                  <AlertTriangle className="w-5 h-5 text-yellow-500" />
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Resources Tab Component
const ResourcesTab = ({ branch }) => {
  const mockEquipment = [
    { name: "Container Cranes", count: 4, status: "Operational", utilization: 85 },
    { name: "Reach Stackers", count: 6, status: "Operational", utilization: 72 },
    { name: "Forklifts", count: 8, status: "Operational", utilization: 68 },
    { name: "Terminal Tractors", count: 12, status: "Operational", utilization: 90 },
    { name: "Mobile Cranes", count: 2, status: "Maintenance", utilization: 0 }
  ];

  return (
    <div className="bg-white rounded-2xl shadow-xl p-6 border border-purple-100">
      <h3 className="text-xl font-bold text-purple-900 mb-4 flex items-center gap-2">
        <Package className="w-5 h-5" />
        Equipment & Resources
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {mockEquipment.map((equipment, idx) => (
          <div key={idx} className="border border-purple-100 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-semibold text-purple-900">{equipment.name}</h4>
              <span className={`px-2 py-1 rounded-full text-xs font-bold ${
                equipment.status === 'Operational' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
              }`}>
                {equipment.status}
              </span>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Count:</span>
                <span className="font-semibold">{equipment.count}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Utilization:</span>
                <span className="font-semibold">{equipment.utilization}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full ${
                    equipment.utilization > 80 ? 'bg-red-500' :
                    equipment.utilization > 60 ? 'bg-yellow-500' : 'bg-green-500'
                  }`}
                  style={{width: `${equipment.utilization}%`}}
                ></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Personnel Tab Component
const PersonnelTab = ({ branch }) => {
  const mockStaff = [
    { department: "Operations", count: 85, shift: "24/7", supervisor: "Rajesh Kumar" },
    { department: "Maintenance", count: 24, shift: "Day", supervisor: "Amit Patel" },
    { department: "Security", count: 36, shift: "24/7", supervisor: "Suresh Singh" },
    { department: "Administration", count: 18, shift: "Day", supervisor: "Priya Sharma" },
    { department: "Quality Control", count: 12, shift: "Day", supervisor: "Neha Gupta" }
  ];

  return (
    <div className="bg-white rounded-2xl shadow-xl p-6 border border-purple-100">
      <h3 className="text-xl font-bold text-purple-900 mb-4 flex items-center gap-2">
        <Users className="w-5 h-5" />
        Assigned Staff
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {mockStaff.map((dept, idx) => (
          <div key={idx} className="border border-purple-100 rounded-lg p-4">
            <h4 className="font-semibold text-purple-900 mb-3">{dept.department}</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Staff Count:</span>
                <span className="font-semibold">{dept.count}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Shift Pattern:</span>
                <span className="font-semibold">{dept.shift}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Supervisor:</span>
                <span className="font-semibold">{dept.supervisor}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Reports Tab Component
const ReportsTab = ({ branch }) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Throughput Stats */}
      <div className="bg-white rounded-2xl shadow-xl p-6 border border-purple-100">
        <h3 className="text-xl font-bold text-purple-900 mb-4 flex items-center gap-2">
          <BarChart3 className="w-5 h-5" />
          Throughput Statistics
        </h3>
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Daily Average:</span>
            <span className="font-bold text-green-600">1,250 TEU</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Monthly Total:</span>
            <span className="font-bold text-blue-600">37,500 TEU</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Efficiency Rate:</span>
            <span className="font-bold text-purple-600">92%</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Downtime:</span>
            <span className="font-bold text-red-600">2.5 hours</span>
          </div>
        </div>
      </div>

      {/* Shift Activity */}
      <div className="bg-white rounded-2xl shadow-xl p-6 border border-purple-100">
        <h3 className="text-xl font-bold text-purple-900 mb-4 flex items-center gap-2">
          <Clock className="w-5 h-5" />
          Shift Activity
        </h3>
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Morning Shift (06:00-14:00):</span>
            <span className="font-bold text-green-600">450 TEU</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Evening Shift (14:00-22:00):</span>
            <span className="font-bold text-blue-600">520 TEU</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Night Shift (22:00-06:00):</span>
            <span className="font-bold text-purple-600">280 TEU</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Peak Hour:</span>
            <span className="font-bold text-orange-600">15:00-16:00</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BranchDetailsTabs;
