import React from "react";

export default function LoadingUnloadingSection() {
  const activities = [
  {
    id: 1,
    vessel: "MV Horizon",
    activity: "Loading",
    start: "08:00",
    expectedEnd: "12:00",
    actualEnd: null,
    status: "In Progress",
  },
  {
    id: 2,
    vessel: "SS Oceanic",
    activity: "Unloading",
    start: "07:00",
    expectedEnd: "10:00",
    actualEnd: "10:15",
    status: "Completed",
  },
  {
    id: 3,
    vessel: "Dock B",
    activity: "Loading",
    start: "09:30",
    expectedEnd: "11:30",
    actualEnd: null,
    status: "Delayed",
  },
  {
    id: 4,
    vessel: "MV Atlantic",
    activity: "Unloading",
    start: "10:00",
    expectedEnd: "13:00",
    actualEnd: null,
    status: "In Progress",
  },
  {
    id: 5,
    vessel: "SS Neptune",
    activity: "Loading",
    start: "06:30",
    expectedEnd: "09:30",
    actualEnd: "09:45",
    status: "Completed",
  },
];


  const statusColors = {
    "In Progress": "bg-yellow-100 text-yellow-800",
    Completed: "bg-green-100 text-green-800",
    Delayed: "bg-red-100 text-red-800",
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 flex-1 min-w-[300px] border border-purple-100">
      <div className="flex items-center mb-4">
        <span className="text-2xl mr-2 text-purple-600">⏳</span>
        <h2 className="font-bold text-lg text-purple-900">Loading / Unloading</h2>
      </div>
      <div className="mb-3 text-sm text-purple-700">Current Activities</div>
      <div className="space-y-4">
        {activities.map((act) => (
          <div key={act.id} className="bg-purple-50 rounded-lg p-4 border border-purple-100">
            <div className="flex justify-between items-center mb-2">
              <div className="font-semibold text-purple-800">{act.vessel}</div>
              <span className={`px-3 py-1 rounded-full text-xs font-medium ${statusColors[act.status]}`}>
                {act.status}
              </span>
            </div>
            <div className="flex flex-wrap items-center text-xs text-purple-600 gap-x-3 gap-y-1">
              <span className="font-medium">{act.activity}</span>
              <span>• Start: {act.start}</span>
              <span>• Expected: {act.expectedEnd}</span>
              {act.actualEnd && <span>• Actual: {act.actualEnd}</span>}
            </div>
            <div className="w-full bg-purple-200 rounded-full h-2 mt-3">
              <div
                className={`h-2 rounded-full ${
                  act.status === "Completed"
                    ? "bg-green-500 w-full"
                    : act.status === "Delayed"
                    ? "bg-red-500 w-2/3"
                    : "bg-yellow-500 w-3/4"
                }`}
              ></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
