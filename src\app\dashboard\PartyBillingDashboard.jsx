import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Tooltip, ResponsiveContainer } from "recharts";

export default function PartyBillingDashboard() {
  // Demo data
  const data = [
    { name: "ABC Ltd. (Customs)", value: 12000 },
    { name: "XYZ Exports (Freight)", value: 9000 },
    { name: "Port CHA (Handling)", value: 4000 },
    { name: "Global Shipping (Storage)", value: 7500 },
    { name: "Marine Services (Maintenance)", value: 3000 },
  ];

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const total = data.reduce((sum, entry) => sum + entry.value, 0);
      const percentage = ((payload[0].value / total) * 100).toFixed(2);
      return (
        <div className="p-3 bg-white border border-gray-300 rounded-lg shadow-lg">
          <p className="text-gray-800 font-semibold">{`${payload[0].name}`}</p>
          <p className="text-gray-600">{`Value: ${payload[0].value} ₹`}</p>
          <p className="text-gray-600">{`Percentage: ${percentage}%`}</p>
        </div>
      );
    }
    return null;
  };

  const COLORS = ["#6366f1", "#34d399", "#facc15", "#ef4444", "#06b6d4"];
  return (
    <div className="bg-white rounded-2xl shadow-xl p-6 mb-6 transform transition-all duration-300 hover:scale-[1.01] hover:shadow-2xl">
      <h2 className="font-extrabold text-2xl mb-4 flex items-center gap-3 text-indigo-700 border-b border-indigo-100 pb-3">👥 Party-Wise Billing Reports</h2>
      <div className="flex flex-col md:flex-row justify-between items-center gap-4">
        <div className="flex flex-col gap-2 pl-16 md:w-1/2">
          {data.map((d, idx) => (
            <div key={d.name} className="flex items-center gap-2 text-base font-medium text-gray-700">
              <span className="inline-block w-4 h-4 rounded-full" style={{ background: COLORS[idx] }}></span>
              {d.name}: <span className="font-semibold text-lg">{d.value} ₹</span>
            </div>
          ))}
        </div>
        <div className="h-72 w-full md:w-1/2">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie data={data} dataKey="value" nameKey="name" cx="50%" cy="50%" outerRadius={100} labelLine={false}>
                {data.map((entry, idx) => (
                  <Cell key={`cell-${idx}`} fill={COLORS[idx % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>
      <button className="mt-6 w-full bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-3 px-4 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-102">Download PDF</button>
    </div>
  );
}
