import { filings } from "@/data/cha";
import React from "react";
import { TableWrapper, Table, TableRow, TableCell } from "@/components/common/table";

const BEFilingTracker = () => (
  <TableWrapper title="BE/SB Filing Tracker" headers={["Reference No.", "Type", "Filing Date", "Status"]}>
    {
      filings.length === 0 ? (
        <TableRow>
          <TableCell colSpan={4} className="text-center text-purple-400 py-4">No filings found.</TableCell>
        </TableRow>
      ) : (
        filings.map((f, i) => (
          <TableRow key={i} variant={i % 2 === 0 ? "even" : "odd"}>
            <TableCell className="font-mono">{f.refNo}</TableCell>
            <TableCell>{f.type}</TableCell>
            <TableCell>{f.date}</TableCell>
            <TableCell>
              <span className={`inline-block px-3 py-1 rounded-full text-xs font-semibold shadow ${f.status === "Filed" ? "bg-green-100 text-green-700" : "bg-yellow-100 text-yellow-700"}`}>
                {f.status}
              </span>
            </TableCell>
          </TableRow>
        ))
      )
    }
  </TableWrapper>
);

export default BEFilingTracker;
