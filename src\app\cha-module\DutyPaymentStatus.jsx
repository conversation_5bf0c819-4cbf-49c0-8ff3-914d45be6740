import React, { useState } from "react";

// Dummy data for multiple duty and delivery payments
const dutyPayments = [
  {
    status: "Paid",
    date: "2025-06-20",
    amount: 12000,
    receipt: "DUTY-001",
    description: "Full payment for import duties on cargo XYZ.",
  },
  {
    status: "Partially Paid",
    date: "2025-06-25",
    amount: 8000,
    receipt: "DUTY-002",
    description: "Partial payment for customs duties, remaining balance due next week.",
  },
  {
    status: "Unpaid",
    date: "",
    amount: 5000,
    receipt: "-",
    description: "Outstanding payment for recent shipment, awaiting clearance.",
  },
  {
    status: "Paid",
    date: "2025-06-15",
    amount: 7500,
    receipt: "DUTY-003",
    description: "Completed payment for hazardous materials handling fee.",
  },
  {
    status: "Partially Paid",
    date: "2025-06-28",
    amount: 3000,
    receipt: "DUTY-004",
    description: "Initial deposit for new consignment duties.",
  },
].reverse(); // Reverse the order

const deliveryPayments = [
  {
    status: "Paid",
    date: "2025-06-18",
    amount: 3000,
    receipt: "DEL-001",
    description: "Delivery charges settled for express shipment.",
  },
  {
    status: "Partially Paid",
    date: "2025-06-22",
    amount: 1500,
    receipt: "DEL-002",
    description: "Remaining delivery fee for oversized package.",
  },
  {
    status: "Paid",
    date: "2025-06-10",
    amount: 2000,
    receipt: "DEL-003",
    description: "Final payment for last-mile delivery service.",
  },
  {
    status: "Unpaid",
    date: "",
    amount: 1000,
    receipt: "-",
    description: "Pending payment for local transportation.",
  },
].reverse(); // Reverse the order

const statusBadge = (status) => {
  const color = status === "Paid" ? "green" : status === "Partially Paid" ? "yellow" : "red";
  return `inline-block px-3 py-1 rounded-full text-xs font-semibold shadow bg-${color}-100 text-${color}-700`;
};

const PaymentList = ({ title, payments }) => {
  const [isOpen, setIsOpen] = useState(false); // State to manage accordion open/close

  return (
    <div className="bg-white rounded-xl shadow p-4 mb-4">
      <div className="flex justify-between items-center cursor-pointer" onClick={() => setIsOpen(!isOpen)}> {/* Clickable header */}
        <h3 className="font-bold text-purple-900 text-lg">{title}</h3>
        <svg
          className={`w-5 h-5 text-purple-600 transform transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </div>

      <div className="mt-4 pt-4 border-t border-gray-200 grid grid-cols-2 gap-2"> {/* Summary always visible */}
        <h4 className="font-semibold text-purple-800 col-span-2">Summary</h4>
        <p className="text-sm text-gray-700">Total payments: <span className="font-medium">{payments.length}</span></p>
        <p className="text-sm text-gray-700">Total amount: <span className="font-medium">₹{payments.reduce((sum, p) => sum + (p.amount || 0), 0)}</span></p>
        <p className="text-sm text-gray-700">Paid: <span className="font-medium text-green-700">{payments.filter(p => p.status === 'Paid').length}</span></p>
        <p className="text-sm text-gray-700">Partially Paid: <span className="font-medium text-yellow-700">{payments.filter(p => p.status === 'Partially Paid').length}</span></p>
        <p className="text-sm text-gray-700">Unpaid: <span className="font-medium text-red-700">{payments.filter(p => p.status === 'Unpaid').length}</span></p>
      </div>

      {isOpen && (
        <div className="space-y-4 mt-4"> {/* Detailed list, conditionally rendered */}
          {payments.map((p, idx) => (
            <div key={idx} className="flex flex-col border-b last:border-b-0 pb-4 last:pb-0">
              <div className="flex flex-wrap items-center gap-x-4 gap-y-2 mb-2">
                <span className={statusBadge(p.status)}>{p.status}</span>
                {p.date && <span className="text-sm text-purple-500">on {p.date}</span>}
                <div className="ml-auto text-right">
                  <span className="text-base font-semibold text-purple-900 block">Amount: ₹{p.amount || 0}</span>
                  <span className="text-xs text-purple-600">Receipt: {p.receipt || '-'}</span>
                </div>
              </div>
              {p.description && <div className="text-sm text-gray-700">Description: {p.description}</div>}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const DutyPaymentStatus = () => (
  <div className="grid grid-cols-1 gap-6"> {/* Reverted to single column layout */}
    <PaymentList title="Duty Payments" payments={dutyPayments} />
    <PaymentList title="Delivery Charges" payments={deliveryPayments} />
  </div>
);

export default DutyPaymentStatus;
