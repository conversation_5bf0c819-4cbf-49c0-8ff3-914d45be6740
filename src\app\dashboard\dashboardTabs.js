import React from "react";

const dashboardTabs = [
  {
    key: "vessel",
    label: "Vessel Summary",
    icon: "🚢",
    component: React.lazy(() => import("./VesselSummaryDashboard")),
  },
  {
    key: "daily",
    label: "Daily Ops",
    icon: "📅",
    component: React.lazy(() => import("./DailySummaryDashboard")),
  },
  {
    key: "party",
    label: "Party Billing",
    icon: "👥",
    component: React.lazy(() => import("./PartyBillingDashboard")),
  },
  {
    key: "labour",
    label: "Labour Productivity",
    icon: "🧑‍🔧",
    component: React.lazy(() => import("./LabourProductivityDashboard")),
  },
  {
    key: "equipment",
    label: "Equipment Utilization",
    icon: "🚜",
    component: React.lazy(() => import("./EquipmentUtilizationDashboard")),
  },
  {
    key: "storage",
    label: "Storage Occupancy",
    icon: "🏗️",
    component: React.lazy(() => import("./StorageOccupancyDashboard")),
  },
  {
    key: "customs",
    label: "Customs Clearance",
    icon: "🛃",
    component: React.lazy(() => import("./CustomsClearanceDashboard")),
  },
];

export default dashboardTabs;
