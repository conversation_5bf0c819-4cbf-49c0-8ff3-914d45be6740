import React from "react";
import { Card } from "@/components/ui/card";

export default function ViewModal({ object, onClose, title = "Details" }) {
  if (!object) return null;
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
      <Card className="w-full max-w-lg p-8 relative rounded-xl border border-purple-200 bg-white shadow-xl backdrop-blur-lg">
        <button
          className="absolute top-4 right-4 text-purple-600 hover:text-purple-800 text-2xl font-bold px-2 py-1 rounded-full focus:outline-none"
          onClick={onClose}
          aria-label="Close"
        >
          ×
        </button>
        <h2 className="text-3xl font-extrabold mb-6 text-purple-900 border-b-2 border-purple-200 pb-3">
          {title}
        </h2>
        <div className="overflow-y-auto max-h-96 pr-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {Object.entries(object).map(([key, value]) => (
              <div key={key} className="bg-purple-50 p-3 rounded-lg shadow-sm">
                <div className="text-xs text-purple-700 font-semibold mb-1 capitalize">
                  {key.replace(/([A-Z])/g, " $1")}
                </div>
                <div className="text-purple-900 font-bold text-sm">{value}</div>
              </div>
            ))}
          </div>
        </div>
      </Card>
    </div>
  );
}
