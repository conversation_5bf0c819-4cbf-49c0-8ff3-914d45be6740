import React, { useState } from "react";
import { TableWrapper, TableRow, TableCell } from "@/components/common/table";
import { Button } from "@/components/ui/button";
import { Eye, Edit, Trash2 } from "lucide-react";
import ViewModal from "@/components/common/view";
import VesselForm from "./VesselForm";
import { FormModal } from "@/components/common/form";
import { ConfirmDialog } from "@/components/common/dialog";
import { dummyVessels, headers, statusColors } from "@/data/vessels";


const VesselTable = ({ vessels, onStatusChange, onRemove }) => {
  const data = vessels && vessels.length > 0 ? vessels : dummyVessels;
  const [viewVessel, setViewVessel] = useState(null);
  const [editVessel, setEditVessel] = useState(null);
  const [deleteVessel, setDeleteVessel] = useState(null);

  const handleEdit = (updated) => {
    if (onAdd) onAdd(updated);
    setEditVessel(null);
  };

  const handleDelete = (idx) => {
    if (onRemove) onRemove(idx);
    setDeleteVessel(null);
  };

  return (
    <>
      <TableWrapper title="Vessels Data" headers={headers}>
        {data.length === 0 ? (
          <TableRow>
            <TableCell colSpan={7} className="text-center text-blue-400 py-8">
              No vessels found.
            </TableCell>
          </TableRow>
        ) : (
          data.map((vessel, idx) => (
            <TableRow
              key={idx}
              variant={idx % 2 === 0 ? "even" : "odd"}
            >
              <TableCell className="font-bold text-purple-900">
                {vessel.vesselName}
              </TableCell>
              <TableCell className="text-purple-700">
                {vessel.imoNumber}
              </TableCell>
              <TableCell className="text-purple-700">
                {vessel.vesselType}
              </TableCell>
              <TableCell className="text-purple-700">{vessel.flag}</TableCell>
              <TableCell className="text-purple-700">
                {vessel.grossTonnage}
              </TableCell>
              <TableCell>
                <span
                  className={`inline-block px-4 py-1 rounded-full border text-xs font-bold tracking-wide shadow transition-colors select-none ${
                    statusColors[vessel.status] ||
                    "bg-purple-50 text-purple-400 border-purple-100"
                  }`}
                >
                  {vessel.status}
                </span>
              </TableCell>
              <TableCell>
                <div className="flex gap-2 justify-center">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setViewVessel(vessel)}
                    className="text-purple-500 hover:bg-purple-100 hover:text-purple-700 rounded-full p-2"
                    title="View"
                  >
                    <Eye className="w-5 h-5" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setEditVessel({ ...data[idx], idx })}
                    className="text-green-500 hover:bg-green-100 hover:text-green-700 rounded-full p-2"
                    title="Edit"
                  >
                    <Edit className="w-5 h-5" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setDeleteVessel({ ...vessel, idx })}
                    className="text-red-500 hover:bg-red-100 hover:text-red-700 rounded-full p-2"
                    title="Delete"
                  >
                    <Trash2 className="w-5 h-5" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          )))}
        </TableWrapper>
      {/* View Modal */}
      {viewVessel && (
        <ViewModal
          object={viewVessel}
          onClose={() => setViewVessel(null)}
          title="Vessel Details"
        />
      )}
      {/* Edit Modal (reuse VesselForm in modal) */}
      {editVessel && (
        <FormModal
          open={!!editVessel}
          onClose={() => setEditVessel(null)}
          title="Edit Vessel"
        >
          <VesselForm
            onAdd={(updated) => handleEdit({ ...updated, idx: editVessel.idx })}
            initialData={editVessel}
            onClose={() => setEditVessel(null)}
            isEdit
          />
        </FormModal>
      )}
      {/* Delete Dialog */}
      {deleteVessel && (
        <ConfirmDialog
          open={!!deleteVessel}
          title="Delete Vessel"
          message={`Are you sure you want to delete "${deleteVessel.vesselName}"? This action cannot be undone.`}
          onCancel={() => setDeleteVessel(null)}
          onConfirm={() => handleDelete(deleteVessel.idx)}
        />
      )}
    </>
  );
};

export default VesselTable;
