"use client";

import { useState } from "react";
import VesselForm from "./VesselForm";
import { FormModal } from "@/components/common/form";
import VesselTable from "./VesselTable";
import { Button } from "@/components/ui/button";

const VesselManagement = () => {
  const [vessels, setVessels] = useState([]);
  const [modalOpen, setModalOpen] = useState(false);

  const handleAdd = (vessel) => {
    setVessels((prev) => [...prev, vessel]);
    setModalOpen(false);
  };

  const handleStatusChange = (idx, status) => {
    setVessels((prev) =>
      prev.map((v, i) => (i === idx ? { ...v, status } : v))
    );
  };

  const handleRemove = (idx) => {
    setVessels((prev) => prev.filter((_, i) => i !== idx));
  };

  return (
   <div>
      <h1 className="text-4xl font-extrabold text-indigo-800 mb-6 flex items-center gap-3 border-b-2 border-indigo-200 pb-4">
        🚢 Vessel Managements
      </h1>
      <div className="flex justify-end mb-4">
        <Button
          className="inline-flex items-center justify-center rounded-md px-6 py-2 text-sm font-medium text-white shadow transition-colors hover:focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:ring-offset-2"
          onClick={() => setModalOpen(true)}
        >
          Add Vessel
        </Button>
      </div>
      <FormModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        title="Add Vessel"
      >
        <VesselForm onAdd={handleAdd} />
      </FormModal>
      <VesselTable
        vessels={vessels}
        onStatusChange={handleStatusChange}
        onRemove={handleRemove}
      />
    </div>
  );
};

export default VesselManagement;
