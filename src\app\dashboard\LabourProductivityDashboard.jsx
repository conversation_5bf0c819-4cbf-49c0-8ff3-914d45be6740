import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer } from "recharts";

export default function LabourProductivityDashboard() {
  // Demo data for three shifts
  const data = [
    { shift: "Morning", hours: 80, jobs: 12, idle: 10 },
    { shift: "Evening", hours: 75, jobs: 10, idle: 15 },
    { shift: "Night", hours: 70, jobs: 11, idle: 12 },
  ];

  // Calculate total hours for each shift
  const shiftTotals = data.map(shiftData => shiftData.hours);
  const grandTotalHours = shiftTotals.reduce((sum, current) => sum + current, 0);

  return (
    <div className="bg-white rounded-2xl shadow-xl p-6 mb-6 transform transition-all duration-300 hover:scale-[1.01] hover:shadow-2xl">
      <h2 className="font-extrabold text-2xl mb-4 flex items-center gap-3 text-indigo-700 border-b border-indigo-100 pb-3">🧑‍🔧 Labour Productivity</h2>
      <div className="flex flex-col md:flex-row gap-6">
        {/* Contribution Percentage Section */}
        <div className="md:w-1/3 flex flex-col gap-4">
          {data.map((shiftData) => {
            const shiftHours = shiftData.hours;
            const percentage = ((shiftHours / grandTotalHours) * 100).toFixed(1);

            return (
              <div key={shiftData.shift} className="bg-gray-50 p-4 rounded-lg shadow-sm">
                <h3 className="font-bold text-lg text-gray-800 mb-2">{shiftData.shift} Shift</h3>
                <p className="text-sm text-gray-600">Contribution: {percentage}%</p>
              </div>
            );
          })}
        </div>

        {/* Graph Section */}
        <div className="md:w-2/3 h-72 w-full">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }} barCategoryGap="20%">
              <XAxis dataKey="shift" axisLine={false} tickLine={false} className="text-sm text-gray-600" />
              <YAxis axisLine={false} tickLine={false} className="text-sm text-gray-600" />
              <Tooltip cursor={{ fill: 'transparent' }} contentStyle={{ borderRadius: '8px', border: 'none', boxShadow: '0 4px 12px rgba(0,0,0,0.1)' }} />
              <Bar dataKey="hours" fill="#6366f1" barSize={30} radius={[10, 10, 0, 0]} name="Hours Worked" />
              <Bar dataKey="jobs" fill="#34d399" barSize={30} radius={[10, 10, 0, 0]} name="Jobs" />
              <Bar dataKey="idle" fill="#facc15" barSize={30} radius={[10, 10, 0, 0]} name="Idle Time" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
}
