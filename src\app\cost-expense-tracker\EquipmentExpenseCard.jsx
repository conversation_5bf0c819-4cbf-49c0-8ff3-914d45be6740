import React from "react";
import { TableWrapper, Table, TableRow, TableCell } from "@/components/common/table";

export default function EquipmentExpenseCard() {
  // Demo data
  const items = [
    { id: 1, type: "Crane", hours: 5, rate: 500, vendor: "LiftCo", total: 2500 },
    { id: 2, type: "Forklift", hours: 3, rate: 300, vendor: "ABC Equip", total: 900 },
    { id: 3, type: "Loader", hours: 4, rate: 350, vendor: "XYZ Rentals", total: 1400 },
    { id: 4, type: "Conveyor", hours: 2, rate: 200, vendor: "ConveyPro", total: 400 },
    { id: 5, type: "Reach Stacker", hours: 6, rate: 600, vendor: "StackerMax", total: 3600 },
  ];
  return (
    <div className="bg-white rounded-xl shadow p-5 flex-1 min-w-[250px]">
      <TableWrapper title="🚜 Equipment Rental" headers={["Type", "Hours", "Rate", "Vendor", "Total"]}>
        {items.map(i => (
          <TableRow key={i.id}>
            <TableCell>{i.type}</TableCell>
            <TableCell>{i.hours}</TableCell>
            <TableCell>{i.rate}</TableCell>
            <TableCell>{i.vendor}</TableCell>
            <TableCell>{i.total}</TableCell>
          </TableRow>
        ))}
      </TableWrapper>
    </div>
  );
}
