"use client";

import React, { useState } from "react";
import CargoSection from "./CargoSection";
import LoadingUnloadingSection from "./LoadingUnloadingSection";
import EquipmentSection from "./EquipmentSection";
import LabourSection from "./LabourSection";

export default function StevedoringManagement() {
  const [activeTab, setActiveTab] = useState(0);
  const tabs = [
    { key: "cargo", label: "Cargo", icon: "📦", content: <CargoSection /> },
    {
      key: "loading",
      label: "Loading/Unloading",
      icon: "⏳",
      content: <LoadingUnloadingSection />,
    },
    {
      key: "equipment",
      label: "Equipment",
      icon: "🔧",
      content: <EquipmentSection />,
    },
    { key: "labour", label: "Labour", icon: "👷‍♂️", content: <LabourSection /> },
  ];

  return (
    <div>
      <h1 className="text-4xl font-extrabold text-indigo-800 mb-6 flex items-center gap-3 border-b-2 border-indigo-200 pb-4">
        🛒 Stevedoring Management Dashboard
      </h1>
      <div className="mb-6 border-b border-indigo-100 flex gap-2 overflow-x-auto">
        {tabs.map((tab, idx) => (
          <button
            key={tab.key}
            className={`px-4 py-2 rounded-lg font-bold text-base flex items-center gap-2 transition-all duration-300 ease-in-out focus:outline-none
              ${
                activeTab === idx
                  ? "bg-gradient-to-r from-indigo-600 to-purple-500 text-white shadow-lg border-b-4 border-purple-700"
                  : "bg-white text-gray-700 hover:bg-indigo-50 hover:text-indigo-800 hover:shadow-md"
              }`}
            onClick={() => setActiveTab(idx)}
            type="button"
          >
            <span>{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>
      <div className="bg-transparent">{tabs[activeTab].content}</div>
    </div>
  );
}
