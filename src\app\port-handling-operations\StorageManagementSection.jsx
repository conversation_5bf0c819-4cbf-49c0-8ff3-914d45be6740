import React from "react";
import { useSearchParams } from "next/navigation";
import {
  TableWrapper,
  Table,
  TableRow,
  TableCell,
} from "@/components/common/table";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, Tooltip } from "recharts";

export default function StorageManagementSection() {
  // Demo data
  const storage = [
    {
      id: 1,
      slot: "Yard A-1",
      party: "ABC Ltd.",
      entry: "08:00",
      exit: "10:00",
      duration: 2,
      rent: 500,
      area: 100,
      space: 130,
      vesselName: "MV Ever Given",
      customerName: "ABC Ltd.",
      branchName: "Mumbai",
    },
    {
      id: 2,
      slot: "Shed B-2",
      party: "XYZ Exports",
      entry: "09:30",
      exit: "11:00",
      duration: 1.5,
      rent: 350,
      area: 75,
      space: 100,
      vesselName: "MV Maersk",
      customerName: "XYZ Exports",
      branchName: "Kandla",
    },
    {
      id: 3,
      slot: "Yard A-2",
      party: "PQR Inc.",
      entry: "11:00",
      exit: "12:00",
      duration: 1,
      rent: 200,
      area: 50,
      space: 80,
      vesselName: "MV MSC",
      customerName: "PQR Inc.",
      branchName: "Kolkata",
    },
    {
      id: 4,
      slot: "Shed B-3",
      party: "LMN Co.",
      entry: "13:00",
      exit: "14:30",
      duration: 1.5,
      rent: 400,
      area: 120,
      space: 150,
      vesselName: "MV CMA CGM",
      customerName: "LMN Co.",
      branchName: "Kandla",
    },
  ];
  
  const COLORS = ["#8884d8", "#82ca9d"];
  const searchParams = useSearchParams();
  const branch = searchParams.get("branch");
  const filteredStorage = branch ? storage.filter((s) => s.branchName === branch) : storage;
  
  const totalSpace = filteredStorage.reduce((sum, item) => sum + item.space, 0); // Example total available space
  const usedArea = filteredStorage.reduce((sum, item) => sum + item.area, 0);
  const freeArea = totalSpace - usedArea;
  
  const data = [
    { name: "Used Area", value: usedArea },
    { name: "Free Area", value: freeArea },
  ];

  return (
    <div className="space-y-6">
      <TableWrapper
        title="🏬 Storage Area Assignment"
        headers={[
          "Slot",
          "Customer",
          "Vessel",
          "Branch",
          "Entry",
          "Exit",
          "Duration (hrs)",
          "Rent (₹)",
        ]}
      >
        {filteredStorage.map((s, idx) => (
          <TableRow key={s.id} variant={idx % 2 === 0 ? "even" : "odd"}>
            <TableCell>{s.slot}</TableCell>
            <TableCell>{s.party}</TableCell>
            <TableCell>{s.vesselName}</TableCell>
            <TableCell>{s.branchName}</TableCell>
            <TableCell>{s.entry}</TableCell>
            <TableCell>{s.exit}</TableCell>
            <TableCell>{s.duration}</TableCell>
            <TableCell>{s.rent}</TableCell>
          </TableRow>
        ))}
      </TableWrapper>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        <div className="bg-white rounded shadow p-4">
          <h3 className="font-bold text-xl mb-2">Space Utilization</h3>
          <p className="text-lg">
            Total Space Available:{" "}
            <span className="font-semibold">{totalSpace} sq.m</span>
          </p>
          <p className="text-lg text-blue-600">
            Used Area: <span className="font-semibold">{usedArea} sq.m</span>
          </p>
          <p className="text-lg text-green-600">
            Free Area: <span className="font-semibold">{freeArea} sq.m</span>
          </p>
        </div>
        <div className="bg-white rounded shadow p-4 flex items-center justify-center">
          <ResponsiveContainer width="100%" height={250}>
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={90}
                fill="#8884d8"
                paddingAngle={5}
                dataKey="value"
                label={({ name, percent }) =>
                  `${name}: ${(percent * 100).toFixed(0)}%`
                }
              >
                {data.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={COLORS[index % COLORS.length]}
                  />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
}
