import React from "react";
import { TableWrapper, Table, TableRow, TableCell } from "@/components/common/table";
import { cn } from "@/lib/utils";

export default function LabourEquipmentSection() {
  // Demo data
  const labour = [
    { id: 1, shift: "Day", role: "Loader", count: 12, hours: 8 },
    { id: 2, shift: "Night", role: "Operator", count: 8, hours: 6 },
    { id: 3, shift: "Day", role: "Supervisor", count: 3, hours: 8 },
  ];
  const equipment = [
    { id: 1, name: "Crane X1", hours: 5, output: 20 },
    { id: 2, name: "Loader L2", hours: 3, output: 10 },
    { id: 3, name: "Conveyor C3", hours: 7, output: 5 },
  ];
  return (
    <div className="space-y-6">
      <div>
        <div className="overflow-x-auto">
          <TableWrapper
            title="👥 Labour Headcounts & Shift Hours"
            headers={["Shift", "Role", "Headcount", "Hours"]}
          >
            {labour.map(l => (
              <TableRow key={l.id}>
                <TableCell>{l.shift}</TableCell>
                <TableCell>{l.role}</TableCell>
                <TableCell>{l.count}</TableCell>
                <TableCell>{l.hours}</TableCell>
              </TableRow>
            ))}
          </TableWrapper>
        </div>
      </div>
      <div>
        <div className="overflow-x-auto">
          <TableWrapper
            title="🔩 Equipment Usage & Performance"
            headers={["Equipment", "Hours Used", "Output (T/h)"]}
          >
            {equipment.map(e => (
              <TableRow key={e.id}>
                <TableCell>{e.name}</TableCell>
                <TableCell>{e.hours}</TableCell>
                <TableCell>{e.output}</TableCell>
              </TableRow>
            ))}
          </TableWrapper>
        </div>
      </div>
    </div>
  );
}
