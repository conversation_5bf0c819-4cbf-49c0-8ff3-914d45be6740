import React from "react";
import { useSearchParams } from "next/navigation";
import {
  TableWrapper,
  Table,
  TableRow,
  TableCell,
} from "@/components/common/table";
import { cn } from "@/lib/utils";

export default function LabourEquipmentSection() {
  // Demo data
  const labour = [
    {
      id: 1,
      shift: "Day",
      role: "Loader",
      count: 12,
      hours: 8,
      vesselName: "MV Sea Queen",
      customerName: "ABC Logistics",
      branchName: "Mumbai",
    },
    {
      id: 2,
      shift: "Night",
      role: "Operator",
      count: 8,
      hours: 6,
      vesselName: "MV Ocean Star",
      customerName: "XYZ Shipping",
      branchName: "Kandla",
    },
    {
      id: 3,
      shift: "Day",
      role: "Supervisor",
      count: 3,
      hours: 8,
      vesselName: "MV Blue Pearl",
      customerName: "PQR Shipping",
      branchName: "Kolkata",
    },
  ];
  const equipment = [
    {
      id: 1,
      name: "Crane X1",
      hours: 5,
      output: 20,
      vesselName: "MV Sea Queen",
      customerName: "ABC Logistics",
      branchName: "Mumbai",
    },
    {
      id: 2,
      name: "Loader L2",
      hours: 3,
      output: 10,
      vesselName: "MV Ocean Star",
      customerName: "XYZ Shipping",
      branchName: "Kandla",
    },
    {
      id: 3,
      name: "Conveyor C3",
      hours: 7,
      output: 5,
      vesselName: "MV Blue Pearl",
      customerName: "PQR Shipping",
      branchName: "Cochin",
    },
  ];
  const searchParams = useSearchParams();
  const branch = searchParams.get("branch");
  const filteredLabour = branch
    ? labour.filter((l) => l.branchName === branch)
    : labour;
  const filteredEquipment = branch
    ? equipment.filter((e) => e.branchName === branch)
    : equipment;

  return (
    <div className="space-y-6">
      <div>
        <div className="overflow-x-auto">
          <TableWrapper
            title="👥 Labour Headcounts & Shift Hours"
            headers={[
              "Shift",
              "Role",
              "Headcount",
              "Customer",
              "Vessel",
              "Branch",
              "Hours",
            ]}
          >
            {filteredLabour.map((l) => (
              <TableRow key={l.id}>
                <TableCell>{l.shift}</TableCell>
                <TableCell>{l.role}</TableCell>
                <TableCell>{l.count}</TableCell>
                <TableCell>{l.customerName}</TableCell>
                <TableCell>{l.vesselName}</TableCell>
                <TableCell>{l.branchName}</TableCell>
                <TableCell>{l.hours}</TableCell>
              </TableRow>
            ))}
          </TableWrapper>
        </div>
      </div>
      <div>
        <div className="overflow-x-auto">
          <TableWrapper
            title="🔩 Equipment Usage & Performance"
            headers={[
              "Equipment",
              "Hours Used",
              "Customer",
              "Vessel",
              "Branch",
              "Output (T/h)",
            ]}
          >
            {filteredEquipment.map((e) => (
              <TableRow key={e.id}>
                <TableCell>{e.name}</TableCell>
                <TableCell>{e.hours}</TableCell>
                <TableCell>{e.customerName}</TableCell>
                <TableCell>{e.vesselName}</TableCell>
                <TableCell>{e.branchName}</TableCell>
                <TableCell>{e.output}</TableCell>
              </TableRow>
            ))}
          </TableWrapper>
        </div>
      </div>
    </div>
  );
}
