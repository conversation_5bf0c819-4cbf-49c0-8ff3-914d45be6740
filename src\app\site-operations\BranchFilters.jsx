import React from "react";

const BranchFilters = ({ filters, onFilterChange }) => {
  const branchTypes = [
    { value: "", label: "All Types" },
    { value: "Yard", label: "Yard" },
    { value: "Terminal", label: "Terminal" },
    { value: "Depot", label: "Depot" },
    { value: "Office", label: "Office" }
  ];

  const statusOptions = [
    { value: "", label: "All Status" },
    { value: "Active", label: "Active" },
    { value: "Inactive", label: "Inactive" },
    { value: "Under Maintenance", label: "Under Maintenance" }
  ];

  const handleBranchTypeChange = (e) => {
    onFilterChange({
      ...filters,
      branchType: e.target.value
    });
  };

  const handleStatusChange = (e) => {
    onFilterChange({
      ...filters,
      status: e.target.value
    });
  };

  const clearFilters = () => {
    onFilterChange({
      branchType: "",
      status: ""
    });
  };

  return (
    <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
      {/* Branch Type Filter */}
      <div className="flex flex-col gap-2">
        <label className="text-sm font-semibold text-purple-700">Branch Type</label>
        <select
          value={filters.branchType}
          onChange={handleBranchTypeChange}
          className="px-3 py-2 border border-purple-200 rounded-md bg-white text-purple-700 focus:border-purple-400 focus:ring-2 focus:ring-purple-400 focus:ring-opacity-20 outline-none transition-all min-w-[140px]"
        >
          {branchTypes.map(type => (
            <option key={type.value} value={type.value}>
              {type.label}
            </option>
          ))}
        </select>
      </div>

      {/* Operational Status Filter */}
      <div className="flex flex-col gap-2">
        <label className="text-sm font-semibold text-purple-700">Operational Status</label>
        <select
          value={filters.status}
          onChange={handleStatusChange}
          className="px-3 py-2 border border-purple-200 rounded-md bg-white text-purple-700 focus:border-purple-400 focus:ring-2 focus:ring-purple-400 focus:ring-opacity-20 outline-none transition-all min-w-[160px]"
        >
          {statusOptions.map(status => (
            <option key={status.value} value={status.value}>
              {status.label}
            </option>
          ))}
        </select>
      </div>

      {/* Clear Filters Button */}
      {(filters.branchType || filters.status) && (
        <div className="flex flex-col gap-2">
          <label className="text-sm font-semibold text-transparent">Clear</label>
          <button
            onClick={clearFilters}
            className="px-4 py-2 text-sm font-medium text-purple-600 hover:text-purple-800 hover:bg-purple-50 rounded-md transition-colors"
          >
            Clear Filters
          </button>
        </div>
      )}
    </div>
  );
};

export default BranchFilters;
