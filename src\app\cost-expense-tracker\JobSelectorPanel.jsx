import React, { useState } from "react";

export default function JobSelectorPanel() {
  // Demo job data
  const jobs = [
    { id: "J-001", vessel: "MV Horizon", date: "2025-06-25", cargo: "Coal", party: "ABC Ltd." },
    { id: "J-002", vessel: "SS Oceanic", date: "2025-06-26", cargo: "Fertilizer", party: "XYZ Exports" },
    { id: "J-003", vessel: "MV Neptune", date: "2025-07-01", cargo: "Crude Oil", party: "Global Energy" },
    { id: "J-004", vessel: "Aurora", date: "2025-07-05", cargo: "Grains", party: "AgriCorp" },
  ];

  const [selectedJobId, setSelectedJobId] = useState(jobs[0].id);
  const selectedJob = jobs.find(job => job.id === selectedJobId);

  const handleJobChange = (event) => {
    setSelectedJobId(event.target.value);
  };

  return (
    <div className="bg-white rounded-2xl shadow-xl p-6 mb-6 transform transition-all duration-300 hover:scale-[1.01] hover:shadow-2xl flex flex-col md:flex-row md:items-center gap-6">
      <div className="flex-1">
        <label htmlFor="cargo-job-select" className="block text-sm font-medium text-gray-700 mb-2">Select Cargo Job</label>
        <div className="relative">
          <select
            id="cargo-job-select"
            className="block w-full px-4 py-2 pr-8 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm appearance-none transition duration-150 ease-in-out"
            value={selectedJobId}
            onChange={handleJobChange}
          >
            {jobs.map(job => (
              <option key={job.id} value={job.id}>{job.vessel} ({job.id})</option>
            ))}
          </select>
        </div>
      </div>
      {selectedJob && (
        <div className="flex-1 grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm text-gray-600 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <div className="flex items-center"><span className="font-semibold text-gray-800 w-16">Vessel:</span> <span className="text-indigo-600 font-medium">{selectedJob.vessel}</span></div>
          <div className="flex items-center"><span className="font-semibold text-gray-800 w-16">Date:</span> <span className="text-indigo-600 font-medium">{selectedJob.date}</span></div>
          <div className="flex items-center"><span className="font-semibold text-gray-800 w-16">Cargo:</span> <span className="text-indigo-600 font-medium">{selectedJob.cargo}</span></div>
          <div className="flex items-center"><span className="font-semibold text-gray-800 w-16">Party:</span> <span className="text-indigo-600 font-medium">{selectedJob.party}</span></div>
        </div>
      )}
    </div>
  );
}
