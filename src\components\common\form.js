import * as React from "react";

export function FormModal({
  open,
  onClose,
  title,
  children,
  maxWidth = "max-w-2xl",
}) {
  if (!open) return null;
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
      <div
        className={`w-full ${maxWidth} max-h-[85vh] flex flex-col bg-gradient-to-br from-purple-50 to-white rounded-2xl shadow-2xl border border-purple-200 p-0 overflow-hidden`}
      >
        <div className="flex items-center justify-between px-8 pt-4 pb-4 border-b border-purple-100 bg-gradient-to-r from-purple-100/80 to-white">
          <div className="text-2xl font-extrabold text-purple-900 flex items-center gap-2">
            <span className="inline-block w-2 h-6 bg-purple-500 rounded-full mr-2" />
            {title}
          </div>
          <button
            className="text-purple-400 hover:text-purple-700 text-2xl font-bold px-2 py-1 rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-purple-300"
            onClick={onClose}
            aria-label="Close"
            type="button"
          >
            ×
          </button>
        </div>
        <div className="flex-1 overflow-y-auto min-h-0 bg-white">
          <div className="w-full max-w-3xl mx-auto p-8">{children}</div>
        </div>
      </div>
    </div>
  );
}
