import React, { useState } from "react";
import ViewModal from "@/components/common/view";

export default function TransporterDetailsSection() {
  // Demo data
  const transporters = [
    {
      id: 1,
      name: "FastMove Logistics",
      logo: "https://ui-avatars.com/api/?name=FML&background=0D8ABC&color=fff",
      contact: "<PERSON><PERSON>",
      mobile: "9876543210",
      type: "3PL",
      address: "Plot 12, Ind. Area, Mumbai",
      zones: "West, North",
      gst: "27AAACF1234G1Z5",
      onboard: "2023-01-15",
      status: "Active",
      vehicles: 12,
      rating: 4.5,
    },
    {
      id: 2,
      name: "CargoXpress",
      logo: "https://ui-avatars.com/api/?name=CX&background=F59E42&color=fff",
      contact: "<PERSON><PERSON>",
      mobile: "9988776655",
      type: "Contractual",
      address: "Shed 5, Port Road, Kandla",
      zones: "West, South",
      gst: "24AACX1234L1Z2",
      onboard: "2022-08-10",
      status: "Blacklisted",
      vehicles: 5,
      rating: 2.8,
    },
    {
      id: 3,
      name: "<PERSON><PERSON> Freight",
      logo: "https://ui-avatars.com/api/?name=SF&background=34D399&color=fff",
      contact: "Amit Patel",
      mobile: "9765432109",
      type: "Fleet Owner",
      address: "Unit 8, Transport Nagar, Delhi, 110044",
      zones: "North, Central",
      gst: "07AAADS5678E1Z9",
      onboard: "2023-03-01",
      status: "Active",
      vehicles: 8,
      rating: 4.0,
      lastActivity: "2024-07-22",
      contractExpiry: "2025-03-01",
    },
    {
      id: 4,
      name: "Reliable Carriers",
      logo: "https://ui-avatars.com/api/?name=RC&background=60A5FA&color=fff",
      contact: "Priya Sharma",
      mobile: "9654321098",
      type: "Broker",
      address: "Office 101, Logistics Hub, Chennai, Tamil Nadu, 600001",
      zones: "South, East",
      gst: "33AAAFG9012H1Z3",
      onboard: "2022-11-20",
      status: "Inactive",
      vehicles: 3,
      rating: 3.5,
      lastActivity: "2024-05-15",
      contractExpiry: "2024-11-20",
    },
    {
      id: 5,
      name: "Global Transport",
      logo: "https://ui-avatars.com/api/?name=GT&background=8B5CF6&color=fff",
      contact: "Suresh Reddy",
      mobile: "9123456789",
      type: "3PL",
      address: "Plot 25, Transport Zone, Hyderabad, Telangana, 500001",
      zones: "South, Central, East",
      gst: "36AAACG4567J1Z8",
      onboard: "2023-02-28",
      status: "Active",
      vehicles: 15,
      rating: 4.7,
      lastActivity: "2024-07-21",
      contractExpiry: "2025-02-28",
    },
    {
      id: 6,
      name: "LogiSwift",
      logo: "https://ui-avatars.com/api/?name=LS&background=EC4899&color=fff",
      contact: "Pooja Singh",
      mobile: "9012345678",
      type: "Fleet Owner",
      address: "Warehouse 7, Industrial Park, Bangalore, Karnataka, 560001",
      zones: "South",
      gst: "29AAADH8901K1Z6",
      onboard: "2022-09-10",
      status: "Active",
      vehicles: 7,
      rating: 3.9,
      lastActivity: "2024-07-19",
      contractExpiry: "2025-09-10",
    },
    {
      id: 7,
      name: "QuickShip Logistics",
      logo: "https://ui-avatars.com/api/?name=QL&background=10B981&color=fff",
      contact: "Rahul Verma",
      mobile: "9543210987",
      type: "Broker",
      address: "Unit 3, Business Center, Kolkata, West Bengal, 700001",
      zones: "East, North",
      gst: "19AAAEF2345L1Z4",
      onboard: "2023-04-05",
      status: "Inactive",
      vehicles: 4,
      rating: 3.2,
      lastActivity: "2024-06-25",
      contractExpiry: "2024-10-05",
    },
    {
      id: 8,
      name: "Prime Movers",
      logo: "https://ui-avatars.com/api/?name=PM&background=EF4444&color=fff",
      contact: "Deepak Kumar",
      mobile: "9321098765",
      type: "Contractual",
      address: "Plot 1, Transport Hub, Pune, Maharashtra, 411001",
      zones: "West, South",
      gst: "27AAAGH6789M1Z0",
      onboard: "2022-07-01",
      status: "Active",
      vehicles: 10,
      rating: 4.1,
      lastActivity: "2024-07-23",
      contractExpiry: "2025-07-01",
    },
  ];
  const statusColors = {
    Active: "bg-green-100 text-green-800",
    Inactive: "bg-gray-100 text-gray-700",
    Blacklisted: "bg-red-100 text-red-800",
  };
  const [selectedTransporter, setSelectedTransporter] = useState(null);
  const fieldsToBeShown = selectedTransporter
    ? Object.fromEntries(
        Object.entries(selectedTransporter).filter(([key]) => key !== "logo")
      )
    : {};

  return (
    <div>
      <div className="rounded-xl border overflow-y-auto border-purple-200 bg-gradient-to-br from-white/70 via-white/60 to-purple-50/40 shadow-xl backdrop-blur-lg p-5 grid md:grid-cols-2 gap-6">
        {transporters.map((t) => (
          <div
            key={t.id}
            className="bg-white rounded-xl shadow p-5 flex flex-col md:flex-row gap-4 items-center border border-purple-100"
          >
            <img
              src={t.logo}
              alt={t.name}
              className="w-16 h-16 rounded-full border-2 border-purple-300 p-1"
            />
            <div className="flex-1 w-full">
              <div className="flex items-center justify-between mb-2">
                <span className="font-bold text-xl text-purple-800">
                  {t.name}
                </span>
                <span
                  className={`px-3 py-1 rounded-full text-xs font-semibold ${
                    statusColors[t.status]
                  }`}
                >
                  {t.status}
                </span>
              </div>
              <div className="grid grid-cols-2 gap-y-1 gap-x-4 text-sm text-gray-700 mb-3">
                <div>
                  <span className="font-semibold">Contact:</span> {t.contact} (
                  {t.mobile})
                </div>
                <div>
                  <span className="font-semibold">Type:</span> {t.type}
                </div>
                <div>
                  <span className="font-semibold">Zones:</span> {t.zones}
                </div>
                <div>
                  <span className="font-semibold">GST:</span> {t.gst}
                </div>
                <div>
                  <span className="font-semibold">Onboarded:</span> {t.onboard}
                </div>
                <div className="col-span-2">
                  <span className="font-semibold">Address:</span> {t.address}
                </div>
              </div>
              <div className="flex items-center gap-4 mt-2 text-purple-700">
                <span className="text-sm font-semibold">
                  Vehicles: {t.vehicles}
                </span>
                <span className="text-sm font-semibold flex items-center">
                  <span className="text-yellow-500 mr-1">★</span> {t.rating}
                </span>
              </div>
              <button
                onClick={() => setSelectedTransporter(t)}
                className="mt-3 px-4 py-2 bg-purple-600 text-white text-xs rounded hover:bg-purple-700 transition-colors"
              >
                View Details
              </button>
            </div>
          </div>
        ))}
      </div>
      {selectedTransporter && (
        <ViewModal
          object={fieldsToBeShown}
          onClose={() => setSelectedTransporter(null)}
          title="Transporter Details"
        />
      )}
    </div>
  );
}
