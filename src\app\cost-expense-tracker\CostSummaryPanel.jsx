import React from "react";
// Example: using lucide-react for icons and a simple SVG for the chart
import { <PERSON><PERSON>hart, Pie, Cell, Tooltip, ResponsiveContainer } from "recharts";

export default function CostSummaryPanel() {
  // Demo totals
  const totals = [
    { name: "Labour", value: 2500, color: "#3b82f6" },
    { name: "Equipment", value: 3400, color: "#22c55e" },
    { name: "Transport", value: 4800, color: "#eab308" },
    { name: "Storage", value: 4100, color: "#a21caf" },
    { name: "Misc.", value: 700, color: "#6b7280" },
  ];
  const grandTotal = totals.reduce((a, b) => a + b.value, 0);

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const total = totals.reduce((sum, entry) => sum + entry.value, 0);
      const percentage = ((payload[0].value / total) * 100).toFixed(2);
      return (
        <div className="p-3 bg-white border border-gray-300 rounded-lg shadow-lg">
          <p className="text-gray-800 font-semibold">{`${payload[0].name}`}</p>
          <p className="text-gray-600">{`Value: ${payload[0].value} ₹`}</p>
          <p className="text-gray-600">{`Percentage: ${percentage}%`}</p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="bg-white rounded-2xl shadow-xl p-6 min-w-[250px] flex flex-col items-center transform transition-all duration-300 hover:scale-[1.01] hover:shadow-2xl">
      <h3 className="font-extrabold text-2xl mb-4 flex items-center gap-2 text-indigo-700 border-b border-indigo-100 pb-3 w-full">Summary</h3>
      <div className="flex flex-col md:flex-row items-center justify-between w-full mb-4">
        <div className="w-full md:w-1/2 space-y-2 mb-4 md:mb-0">
          {totals.map(t => (
            <div key={t.name} className="flex items-center justify-between text-md">
              <span className="flex items-center gap-2 font-medium text-gray-700">
                <span className="ml-24 w-3 h-3 rounded-full shadow-sm" style={{ background: t.color }}></span>
                {t.name}: ₹{t.value} 
                </span>
            </div>
          ))}
        </div>
        <div className="h-48 w-full md:w-1/2">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie data={totals} dataKey="value" nameKey="name" cx="50%" cy="50%" outerRadius={80} labelLine={false}>
                {totals.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>
      <div className="mt-4 pt-4 border-t border-indigo-100 flex justify-around items-center w-full">
        <p className="font-extrabold text-xl text-indigo-700">Grand Total: {grandTotal} ₹</p>
        <button className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">Export</button>
      </div>
    </div>
  );
}
