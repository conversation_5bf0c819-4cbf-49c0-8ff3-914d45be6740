"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { ArrowLeft, Ship, Package, MapPin, User, Calendar, Clock } from "lucide-react";
import { Button } from "@/components/ui/button";
import ShipmentDetailsTabs from "./ShipmentDetailsTabs";

const ShipmentDetailsPage = () => {
  const router = useRouter();
  const params = useParams();
  const [shipment, setShipment] = useState(null);
  const [loading, setLoading] = useState(true);

  // Mock data - in real app, this would fetch from API
  const mockShipments = {
    1: {
      id: 1,
      shipmentId: "FF-2024-001",
      cargoType: "Electronics",
      origin: "Shanghai, China",
      destination: "Los Angeles, USA",
      vesselName: "MSC Gulsun",
      customerName: "TechCorp Inc.",
      status: "In Transit",
      eta: "2024-01-15",
      bookingDate: "2024-01-01",
      weight: "25.5 MT",
      containerType: "40ft HC",
      customerEmail: "<EMAIL>",
      customerPhone: "******-0123",
      specialInstructions: "Handle with care - fragile electronics",
      trackingNumber: "MSC123456789",
      portOfLoading: "Shanghai Port",
      portOfDischarge: "Port of Los Angeles",
      estimatedDeparture: "2024-01-05",
      actualDeparture: "2024-01-05",
      currentLocation: "Pacific Ocean",
      progress: 65
    },
    2: {
      id: 2,
      shipmentId: "FF-2024-002",
      cargoType: "Textiles",
      origin: "Mumbai, India",
      destination: "Hamburg, Germany",
      vesselName: "Ever Given",
      customerName: "Fashion Ltd.",
      status: "Booked",
      eta: "2024-01-20",
      bookingDate: "2024-01-02",
      weight: "18.2 MT",
      containerType: "20ft",
      customerEmail: "<EMAIL>",
      customerPhone: "+49-40-123456",
      specialInstructions: "Temperature controlled storage required",
      trackingNumber: "EVR987654321",
      portOfLoading: "Jawaharlal Nehru Port",
      portOfDischarge: "Port of Hamburg",
      estimatedDeparture: "2024-01-10",
      actualDeparture: null,
      currentLocation: "Mumbai Port",
      progress: 15
    }
  };

  useEffect(() => {
    // Simulate API call
    const fetchShipment = () => {
      const shipmentId = parseInt(params.id);
      const shipmentData = mockShipments[shipmentId];
      
      if (shipmentData) {
        setShipment(shipmentData);
      }
      setLoading(false);
    };

    fetchShipment();
  }, [params.id]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-lg text-purple-600">Loading shipment details...</div>
      </div>
    );
  }

  if (!shipment) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-600 mb-4">Shipment Not Found</h2>
        <Button onClick={() => router.back()} variant="outline">
          Go Back
        </Button>
      </div>
    );
  }

  const statusColors = {
    "Booked": "bg-blue-50 text-blue-700 border-blue-200",
    "In Transit": "bg-purple-50 text-purple-700 border-purple-200",
    "Delivered": "bg-green-50 text-green-700 border-green-200",
    "Delayed": "bg-red-50 text-red-700 border-red-200"
  };

  return (
    <div>
      {/* Header with Back Button */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center gap-2 border-purple-200 text-purple-700 hover:bg-purple-50"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Shipments
        </Button>
        <h1 className="text-4xl font-extrabold text-indigo-800 flex items-center gap-3">
          <Ship className="w-8 h-8" />
          {shipment.shipmentId}
        </h1>
      </div>

      {/* Shipment Overview Card */}
      <div className="bg-white rounded-2xl shadow-xl p-6 mb-6 border border-purple-100">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Route */}
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <MapPin className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Route</p>
              <p className="font-semibold text-purple-900">{shipment.origin}</p>
              <p className="text-xs text-gray-500">to {shipment.destination}</p>
            </div>
          </div>

          {/* Cargo & Vessel */}
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Package className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Cargo & Vessel</p>
              <p className="font-semibold text-purple-900">{shipment.cargoType}</p>
              <p className="text-xs text-gray-500">{shipment.vesselName}</p>
            </div>
          </div>

          {/* Customer */}
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <User className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Customer</p>
              <p className="font-semibold text-purple-900">{shipment.customerName}</p>
              <p className="text-xs text-gray-500">{shipment.customerEmail}</p>
            </div>
          </div>

          {/* Status & ETA */}
          <div className="flex items-center gap-3">
            <div className="p-2 bg-orange-100 rounded-lg">
              <Clock className="w-5 h-5 text-orange-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Status & ETA</p>
              <span
                className={`inline-block px-2 py-1 rounded-full border text-xs font-bold tracking-wide ${
                  statusColors[shipment.status] || "bg-gray-50 text-gray-700 border-gray-200"
                }`}
              >
                {shipment.status}
              </span>
              <p className="text-xs text-gray-500 mt-1">ETA: {shipment.eta}</p>
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mt-6 pt-6 border-t border-purple-100">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-bold text-purple-900">Shipment Progress</h3>
            <span className="text-sm font-semibold text-purple-700">{shipment.progress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className="bg-gradient-to-r from-purple-500 to-blue-500 h-3 rounded-full transition-all duration-300"
              style={{width: `${shipment.progress}%`}}
            ></div>
          </div>
          <div className="flex justify-between text-xs text-gray-600 mt-2">
            <span>Origin</span>
            <span>In Transit</span>
            <span>Destination</span>
          </div>
        </div>

        {/* Quick Info */}
        <div className="mt-6 pt-6 border-t border-purple-100">
          <h3 className="text-lg font-bold text-purple-900 mb-4">Quick Information</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-purple-50 rounded-lg">
              <div className="text-xl font-bold text-purple-700">{shipment.weight}</div>
              <div className="text-sm text-gray-600">Weight</div>
            </div>
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-xl font-bold text-blue-700">{shipment.containerType}</div>
              <div className="text-sm text-gray-600">Container</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-xl font-bold text-green-700">{shipment.bookingDate}</div>
              <div className="text-sm text-gray-600">Booked</div>
            </div>
            <div className="text-center p-3 bg-orange-50 rounded-lg">
              <div className="text-xl font-bold text-orange-700">{shipment.currentLocation}</div>
              <div className="text-sm text-gray-600">Current Location</div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabbed Content */}
      <ShipmentDetailsTabs shipment={shipment} />
    </div>
  );
};

export default ShipmentDetailsPage;
