import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

const ShipmentForm = ({ onAdd, initialData = null, isEdit = false }) => {
  const [formData, setFormData] = useState({
    cargoType: initialData?.cargoType || "",
    origin: initialData?.origin || "",
    destination: initialData?.destination || "",
    vesselName: initialData?.vesselName || "",
    customerName: initialData?.customerName || "",
    status: initialData?.status || "Booked",
    eta: initialData?.eta || "",
    weight: initialData?.weight || "",
    containerType: initialData?.containerType || "20ft",
    customerEmail: initialData?.customerEmail || "",
    customerPhone: initialData?.customerPhone || "",
    specialInstructions: initialData?.specialInstructions || ""
  });

  const [errors, setErrors] = useState({});

  const statusOptions = ["Booked", "In Transit", "Delivered", "Delayed"];
  const containerTypes = [
    "20ft",
    "40ft", 
    "40ft HC",
    "20ft Reefer",
    "40ft Reefer",
    "20ft Tank",
    "40ft Tank"
  ];

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.cargoType.trim()) {
      newErrors.cargoType = "Cargo type is required";
    }

    if (!formData.origin.trim()) {
      newErrors.origin = "Origin is required";
    }

    if (!formData.destination.trim()) {
      newErrors.destination = "Destination is required";
    }

    if (!formData.vesselName.trim()) {
      newErrors.vesselName = "Vessel name is required";
    }

    if (!formData.customerName.trim()) {
      newErrors.customerName = "Customer name is required";
    }

    if (!formData.eta) {
      newErrors.eta = "ETA is required";
    }

    if (!formData.weight.trim()) {
      newErrors.weight = "Weight is required";
    }

    if (formData.customerEmail && !/\S+@\S+\.\S+/.test(formData.customerEmail)) {
      newErrors.customerEmail = "Please enter a valid email address";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const shipmentData = {
      ...formData,
      id: initialData?.id || Date.now()
    };

    onAdd(shipmentData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Cargo Type */}
      <div className="space-y-2">
        <Label htmlFor="cargoType" className="text-sm font-semibold text-purple-700">
          Cargo Type *
        </Label>
        <Input
          id="cargoType"
          name="cargoType"
          type="text"
          value={formData.cargoType}
          onChange={handleChange}
          placeholder="e.g., Electronics, Textiles, Machinery"
          className={`bg-white border-purple-200 focus:border-purple-400 focus:ring-purple-400 ${
            errors.cargoType ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
          }`}
        />
        {errors.cargoType && (
          <p className="text-sm text-red-600">{errors.cargoType}</p>
        )}
      </div>

      {/* Origin and Destination */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="origin" className="text-sm font-semibold text-purple-700">
            Origin *
          </Label>
          <Input
            id="origin"
            name="origin"
            type="text"
            value={formData.origin}
            onChange={handleChange}
            placeholder="e.g., Shanghai, China"
            className={`bg-white border-purple-200 focus:border-purple-400 focus:ring-purple-400 ${
              errors.origin ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
            }`}
          />
          {errors.origin && (
            <p className="text-sm text-red-600">{errors.origin}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="destination" className="text-sm font-semibold text-purple-700">
            Destination *
          </Label>
          <Input
            id="destination"
            name="destination"
            type="text"
            value={formData.destination}
            onChange={handleChange}
            placeholder="e.g., Los Angeles, USA"
            className={`bg-white border-purple-200 focus:border-purple-400 focus:ring-purple-400 ${
              errors.destination ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
            }`}
          />
          {errors.destination && (
            <p className="text-sm text-red-600">{errors.destination}</p>
          )}
        </div>
      </div>

      {/* Vessel Name */}
      <div className="space-y-2">
        <Label htmlFor="vesselName" className="text-sm font-semibold text-purple-700">
          Vessel Name *
        </Label>
        <Input
          id="vesselName"
          name="vesselName"
          type="text"
          value={formData.vesselName}
          onChange={handleChange}
          placeholder="e.g., MSC Gulsun"
          className={`bg-white border-purple-200 focus:border-purple-400 focus:ring-purple-400 ${
            errors.vesselName ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
          }`}
        />
        {errors.vesselName && (
          <p className="text-sm text-red-600">{errors.vesselName}</p>
        )}
      </div>

      {/* Customer Information */}
      <div className="space-y-2">
        <Label htmlFor="customerName" className="text-sm font-semibold text-purple-700">
          Customer Name *
        </Label>
        <Input
          id="customerName"
          name="customerName"
          type="text"
          value={formData.customerName}
          onChange={handleChange}
          placeholder="e.g., TechCorp Inc."
          className={`bg-white border-purple-200 focus:border-purple-400 focus:ring-purple-400 ${
            errors.customerName ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
          }`}
        />
        {errors.customerName && (
          <p className="text-sm text-red-600">{errors.customerName}</p>
        )}
      </div>

      {/* Customer Contact Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="customerEmail" className="text-sm font-semibold text-purple-700">
            Customer Email
          </Label>
          <Input
            id="customerEmail"
            name="customerEmail"
            type="email"
            value={formData.customerEmail}
            onChange={handleChange}
            placeholder="<EMAIL>"
            className={`bg-white border-purple-200 focus:border-purple-400 focus:ring-purple-400 ${
              errors.customerEmail ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
            }`}
          />
          {errors.customerEmail && (
            <p className="text-sm text-red-600">{errors.customerEmail}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="customerPhone" className="text-sm font-semibold text-purple-700">
            Customer Phone
          </Label>
          <Input
            id="customerPhone"
            name="customerPhone"
            type="tel"
            value={formData.customerPhone}
            onChange={handleChange}
            placeholder="+1-XXX-XXX-XXXX"
            className="bg-white border-purple-200 focus:border-purple-400 focus:ring-purple-400"
          />
        </div>
      </div>

      {/* Weight and Container Type */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="weight" className="text-sm font-semibold text-purple-700">
            Weight *
          </Label>
          <Input
            id="weight"
            name="weight"
            type="text"
            value={formData.weight}
            onChange={handleChange}
            placeholder="e.g., 25.5 MT"
            className={`bg-white border-purple-200 focus:border-purple-400 focus:ring-purple-400 ${
              errors.weight ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
            }`}
          />
          {errors.weight && (
            <p className="text-sm text-red-600">{errors.weight}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="containerType" className="text-sm font-semibold text-purple-700">
            Container Type
          </Label>
          <select
            id="containerType"
            name="containerType"
            value={formData.containerType}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-purple-200 rounded-md bg-white text-purple-700 focus:border-purple-400 focus:ring-2 focus:ring-purple-400 focus:ring-opacity-20 outline-none transition-all"
          >
            {containerTypes.map(type => (
              <option key={type} value={type}>
                {type}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* ETA and Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="eta" className="text-sm font-semibold text-purple-700">
            ETA *
          </Label>
          <Input
            id="eta"
            name="eta"
            type="date"
            value={formData.eta}
            onChange={handleChange}
            className={`bg-white border-purple-200 focus:border-purple-400 focus:ring-purple-400 ${
              errors.eta ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
            }`}
          />
          {errors.eta && (
            <p className="text-sm text-red-600">{errors.eta}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="status" className="text-sm font-semibold text-purple-700">
            Status
          </Label>
          <select
            id="status"
            name="status"
            value={formData.status}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-purple-200 rounded-md bg-white text-purple-700 focus:border-purple-400 focus:ring-2 focus:ring-purple-400 focus:ring-opacity-20 outline-none transition-all"
          >
            {statusOptions.map(status => (
              <option key={status} value={status}>
                {status}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Special Instructions */}
      <div className="space-y-2">
        <Label htmlFor="specialInstructions" className="text-sm font-semibold text-purple-700">
          Special Instructions
        </Label>
        <textarea
          id="specialInstructions"
          name="specialInstructions"
          value={formData.specialInstructions}
          onChange={handleChange}
          placeholder="Any special handling instructions..."
          rows={3}
          className="w-full px-3 py-2 border border-purple-200 rounded-md bg-white text-purple-700 focus:border-purple-400 focus:ring-2 focus:ring-purple-400 focus:ring-opacity-20 outline-none transition-all resize-none"
        />
      </div>

      {/* Submit Button */}
      <div className="flex justify-end pt-4">
        <Button
          type="submit"
          className="bg-purple-700 hover:bg-purple-800 text-white px-6 py-2"
        >
          {isEdit ? "Update Shipment" : "Create Booking"}
        </Button>
      </div>
    </form>
  );
};

export default ShipmentForm;
