export const filings = [
  { refNo: "BE12345", type: "BE", date: "2025-06-01", status: "Filed" },
  { refNo: "SB67890", type: "SB", date: "2025-06-02", status: "Pending" },
  { refNo: "BE54321", type: "BE", date: "2025-06-05", status: "Filed" },
  { refNo: "SB09876", type: "SB", date: "2025-06-06", status: "Filed" },
  { refNo: "BE11223", type: "BE", date: "2025-06-07", status: "Pending" },
  { refNo: "SB44556", type: "SB", date: "2025-06-08", status: "Rejected" },
  { refNo: "BE77889", type: "BE", date: "2025-06-09", status: "Filed" },
  { refNo: "SB99001", type: "SB", date: "2025-06-10", status: "Filed" },
];


export const duty = {
  status: "Paid",
  date: "2025-06-03",
  amount: 120000,
  receipt: "RCPT123",
};
export const delivery = { status: "Pending", date: "", amount: 0, receipt: "" };

export const documents = [
  {
    name: "Invoice.pdf",
    uploadedAt: "2025-06-01 10:00",
    status: "Cleared",
    clearedBy: "Officer A",
    remarks: "OK",
  },
  {
    name: "PackingList.pdf",
    uploadedAt: "2025-06-01 10:05",
    status: "Pending",
    clearedBy: "",
    remarks: "Awaiting",
  },
  {
    name: "BE_Copy.pdf",
    uploadedAt: "2025-06-02 09:30",
    status: "Cleared",
    clearedBy: "Officer B",
    remarks: "Verified",
  },
  {
    name: "InsuranceCert.pdf",
    uploadedAt: "2025-06-03 12:20",
    status: "Cleared",
    clearedBy: "Officer C",
    remarks: "Valid",
  },
  {
    name: "GatePass.pdf",
    uploadedAt: "2025-06-04 08:45",
    status: "Pending",
    clearedBy: "",
    remarks: "To be approved",
  },
  {
    name: "TransportOrder.pdf",
    uploadedAt: "2025-06-04 09:10",
    status: "Cleared",
    clearedBy: "Officer D",
    remarks: "Ready for dispatch",
  },
];


export const expenses = [
  {
    type: "Duty Charges",
    amount: 120000,
    date: "2025-06-03",
    party: "Customs",
    billNo: "BILL001",
  },
  {
    type: "Handling Fees",
    amount: 5000,
    date: "2025-06-04",
    party: "Agent X",
    billNo: "BILL002",
  },
  {
    type: "Demurrage Fees",
    amount: 2000,
    date: "2025-06-05",
    party: "Port Trust",
    billNo: "BILL003",
  },
  {
    type: "Miscellaneous",
    amount: 800,
    date: "2025-06-06",
    party: "Other",
    billNo: "BILL004",
  },
  {
    type: "Storage Charges",
    amount: 3500,
    date: "2025-06-07",
    party: "Warehouse Co.",
    billNo: "BILL005",
  },
  {
    type: "Transport Fees",
    amount: 6200,
    date: "2025-06-08",
    party: "LogiTrans",
    billNo: "BILL006",
  },
  {
    type: "Container Rent",
    amount: 1500,
    date: "2025-06-09",
    party: "Container Corp",
    billNo: "BILL007",
  },
  {
    type: "Survey Charges",
    amount: 1000,
    date: "2025-06-10",
    party: "Surveyor Y",
    billNo: "BILL008",
  },
  {
    type: "Documentation Fees",
    amount: 1200,
    date: "2025-06-11",
    party: "Agent Z",
    billNo: "BILL009",
  },
  {
    type: "Weighbridge Charges",
    amount: 600,
    date: "2025-06-12",
    party: "Port Trust",
    billNo: "BILL010",
  },
];

