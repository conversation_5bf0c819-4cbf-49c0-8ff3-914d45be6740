import React from "react";
import { TableWrapper, TableRow, TableCell } from "@/components/common/table";

export default function MiscExpenseCard() {
  // Demo data
  const items = [
    { id: 1, type: "Entry Pass", cost: 200, remarks: "For 2 vehicles" },
    { id: 2, type: "Dock Dues", cost: 500, remarks: "Bulk cargo" },
    { id: 3, type: "Permit Fees", cost: 300, remarks: "Night operation" },
    { id: 4, type: "Weighbridge", cost: 150, remarks: "Yard A" },
    { id: 5, type: "Miscellaneous", cost: 100, remarks: "Snacks for staff" },
  ];
  return (
    <div className="bg-white rounded-xl shadow min-w-[250px]">
      <TableWrapper title="🧾 Misc. Port Charges">
        <TableRow>
          <TableCell isHeader>Type</TableCell>
          <TableCell isHeader>Cost</TableCell>
          <TableCell isHeader>Remarks</TableCell>
        </TableRow>
        {items.map(i => (
          <TableRow key={i.id}>
            <TableCell>{i.type}</TableCell>
            <TableCell className="text-center">{i.cost}</TableCell>
            <TableCell>{i.remarks}</TableCell>
          </TableRow>
        ))}
      </TableWrapper>
    </div>
  );
}
