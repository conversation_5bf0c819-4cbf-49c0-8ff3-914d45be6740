import React, { useState } from "react";
// Example: use recharts for charts
import { BarChart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer } from "recharts";

export default function VesselSummaryDashboard() {
  // Dummy data for multiple vessels
  const vesselsData = [
    {
      name: "MV Horizon",
      cargo: "Coal",
      qty: "120T",
      tat: "8h",
      equipment: "Crane, Loader",
      labour: "12",
      delays: "1h",
      idle: "0.5h",
      chartData: [
        { metric: "Time in Port", value: 8 },
        { metric: "Unloading Time", value: 4 },
        { metric: "Idle Time", value: 0.5 },
        { metric: "Delay Time", value: 1 },
      ],
    },
    {
      name: "MV Seafarer",
      cargo: "Grain",
      qty: "150T",
      tat: "10h",
      equipment: "Conveyor",
      labour: "15",
      delays: "0.5h",
      idle: "0.2h",
      chartData: [
        { metric: "Time in Port", value: 10 },
        { metric: "Unloading Time", value: 6 },
        { metric: "Idle Time", value: 0.2 },
        { metric: "Delay Time", value: 0.5 },
      ],
    },
    {
      name: "MV Ocean King",
      cargo: "Oil",
      qty: "200T",
      tat: "12h",
      equipment: "Pumps",
      labour: "10",
      delays: "2h",
      idle: "1h",
      chartData: [
        { metric: "Time in Port", value: 12 },
        { metric: "Unloading Time", value: 7 },
        { metric: "Idle Time", value: 1 },
        { metric: "Delay Time", value: 2 },
      ],
    },
  ];

  const [selectedVessel, setSelectedVessel] = useState(vesselsData[0]);

  const handleVesselChange = (event) => {
    const vesselName = event.target.value;
    const vessel = vesselsData.find((v) => v.name === vesselName);
    setSelectedVessel(vessel);
  };

  return (
    <div className="bg-white rounded-2xl shadow-xl p-6 mb-6 transform transition-all duration-300 hover:scale-[1.01] hover:shadow-2xl">
      <h2 className="font-extrabold text-2xl mb-4 flex items-center gap-3 text-indigo-700 border-b border-indigo-100 pb-3">
        🚢 Vessel-Wise Operation Summary
      </h2>
      <div className="mb-4">
        <label htmlFor="vessel-select" className="block text-lg font-medium text-gray-700 mb-2">Select Vessel:</label>
        <select
          id="vessel-select"
          className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md shadow-sm appearance-none bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition duration-150 ease-in-out pr-8"
          value={selectedVessel.name}
          onChange={handleVesselChange}
        >
          {vesselsData.map((vessel) => (
            <option key={vessel.name} value={vessel.name}>
              {vessel.name}
            </option>
          ))}
        </select>
      </div>
      <div className="flex flex-col lg:flex-row gap-6">
        <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4 text-gray-700">
          <div className="bg-indigo-50 p-3 rounded-lg shadow-sm">Vessel: <span className="font-semibold">{selectedVessel.name}</span></div>
          <div className="bg-indigo-50 p-3 rounded-lg shadow-sm">Cargo: <span className="font-semibold">{selectedVessel.cargo}</span></div>
          <div className="bg-indigo-50 p-3 rounded-lg shadow-sm">Qty: <span className="font-semibold">{selectedVessel.qty}</span></div>
          <div className="bg-indigo-50 p-3 rounded-lg shadow-sm">TAT: <span className="font-semibold">{selectedVessel.tat}</span></div>
          <div className="bg-indigo-50 p-3 rounded-lg shadow-sm">Equipment: <span className="font-semibold">{selectedVessel.equipment}</span></div>
          <div className="bg-indigo-50 p-3 rounded-lg shadow-sm">Labour: <span className="font-semibold">{selectedVessel.labour}</span></div>
          <div className="bg-red-50 p-3 rounded-lg shadow-sm">Delays: <span className="font-semibold text-red-600">{selectedVessel.delays}</span></div>
          <div className="bg-green-50 p-3 rounded-lg shadow-sm">Idle: <span className="font-semibold text-green-600">{selectedVessel.idle}</span></div>
        </div>
        <div className="flex-1 h-64 w-full">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={selectedVessel.chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }} barCategoryGap="20%">
              <XAxis dataKey="metric" axisLine={false} tickLine={false} className="text-sm text-gray-600" />
              <YAxis axisLine={false} tickLine={false} className="text-sm text-gray-600" />
              <Tooltip cursor={{ fill: 'transparent' }} contentStyle={{ borderRadius: '8px', border: 'none', boxShadow: '0 4px 12px rgba(0,0,0,0.1)' }} />
              <Bar dataKey="value" fill="#6366f1" barSize={30} radius={[10, 10, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
}
