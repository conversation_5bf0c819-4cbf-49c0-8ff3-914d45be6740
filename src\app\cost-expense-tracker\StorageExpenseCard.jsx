import React from "react";
import { TableWrapper, TableRow, TableCell } from "@/components/common/table";

export default function StorageExpenseCard() {
  // Demo data
  const items = [
    { id: 1, area: "Yard A-1", start: "2025-06-20", end: "2025-06-25", rate: 500, total: 2500 },
    { id: 2, area: "Shed B-2", start: "2025-06-22", end: "2025-06-26", rate: 400, total: 1600 },
    { id: 3, area: "Yard C-3", start: "2025-06-21", end: "2025-06-27", rate: 600, total: 3600 },
    { id: 4, area: "Shed D-4", start: "2025-06-23", end: "2025-06-28", rate: 450, total: 2250 },
    { id: 5, area: "Yard E-5", start: "2025-06-24", end: "2025-06-29", rate: 550, total: 2750 },
  ];
  return (
    <div className="bg-white rounded-xl shadow min-w-[250px]">
      <TableWrapper title="🏗️ Storage">
        <TableRow>
          <TableCell isHeader>Area</TableCell>
          <TableCell isHeader>Start</TableCell>
          <TableCell isHeader>End</TableCell>
          <TableCell isHeader>Rate</TableCell>
          <TableCell isHeader>Total</TableCell>
        </TableRow>
        {items.map(i => (
          <TableRow key={i.id}>
            <TableCell>{i.area}</TableCell>
            <TableCell>{i.start}</TableCell>
            <TableCell>{i.end}</TableCell>
            <TableCell className="text-center">{i.rate}</TableCell>
            <TableCell className="text-center">{i.total}</TableCell>
          </TableRow>
        ))}
      </TableWrapper>
    </div>
  );
}
