"use client";

import React, { useState, Suspense } from "react";
import invoiceTabs from "./invoiceTabs";

export default function InvoiceGeneration() {
  const [activeTab, setActiveTab] = useState(0);
  const ActiveComponent = invoiceTabs[activeTab].component;
  return (
    <div>
      <h1 className="text-4xl font-extrabold text-indigo-800 mb-6 flex items-center gap-3 border-b-2 border-indigo-200 pb-4">
        🧾 Invoice Generation
      </h1>
      <div className="mb-8 border-b border-indigo-100 flex flex-wrap justify-center gap-3 p-1 bg-white rounded-lg shadow-inner">
        {invoiceTabs.map((tab, idx) => (
          <button
            key={tab.key}
            className={`px-6 py-3 rounded-lg font-bold text-base flex items-center gap-2 transition-all duration-300 ease-in-out focus:outline-none transform hover:scale-105
              ${
                activeTab === idx
                  ? "bg-gradient-to-r from-indigo-600 to-purple-500 text-white shadow-lg border-b-4 border-purple-700"
                  : "bg-white text-gray-700 hover:bg-indigo-50 hover:text-indigo-800"
              }`}
            onClick={() => setActiveTab(idx)}
            type="button"
          >
            <span>{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>
      <div className="bg-transparent">
        <Suspense
          fallback={
            <div className="p-8 text-center text-lg text-gray-600">
              Loading invoice data...
            </div>
          }
        >
          <ActiveComponent />
        </Suspense>
      </div>
    </div>
  );
}
