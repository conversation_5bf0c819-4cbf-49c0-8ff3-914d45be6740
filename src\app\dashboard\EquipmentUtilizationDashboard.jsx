import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer } from "recharts";

export default function EquipmentUtilizationDashboard() {
  const data = [
    { area: "Crane 1", occupied: 180, total: 200, color: "#3b82f6" },
    { area: "Forklift 2", occupied: 150, total: 250, color: "#22c55e" },
    { area: "Stacker 3", occupied: 220, total: 300, color: "#eab308" },
    { area: "Tractor 4", occupied: 90, total: 120, color: "#a21caf" },
  ];

  const calculatePercentage = (occupied, total) => {
    return ((occupied / total) * 100).toFixed(2);
  };

  const processedData = data.map(item => ({
    ...item,
    percentage: (item.occupied / item.total) * 100,
  }));

  return (
    <div className="bg-white rounded-xl shadow p-5 mb-6 w-full">
      <h2 className="font-bold text-lg mb-4 flex items-center gap-2 text-gray-800">
        <span className="text-2xl">🚜</span> Equipment Utilization
      </h2>
      <div className="flex flex-col md:flex-row items-center justify-between w-full">
        <div className="flex flex-col gap-4 mb-4 md:mb-0 md:w-1/2 pl-16">
          {data.map((d, idx) => (
            <div key={d.area} className="flex items-center gap-2 text-base font-medium text-gray-700">
              <span className="inline-block w-4 h-4 rounded-full" style={{ background: d.color }}></span>
              {d.area}: {d.occupied} out of {d.total}
            </div>
          ))}
        </div>
        <div className="h-48 w-full md:w-1/2">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <XAxis dataKey="area" axisLine={false} tickLine={false} />
              <YAxis axisLine={false} tickLine={false} />
              <Tooltip formatter={(value, name, props) => [`${props.payload.occupied} out of ${props.payload.total} (${calculatePercentage(props.payload.occupied, props.payload.total)}%)`, "Utilization"]} />
              <Bar dataKey="occupied" fill={(entry) => entry.color} name="Utilization" barSize={30} radius={[5, 5, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
}
