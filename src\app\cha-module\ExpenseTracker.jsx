import { expenses } from "@/data/cha";
import React from "react";
import { TableWrapper, TableRow, TableCell } from "@/components/common/table";

const ExpenseTracker = () => {
  const total = expenses.reduce((sum, e) => sum + (e.amount || 0), 0);
  const headers = ["Type", "Amount", "Date", "Party", "Bill No."];
  return (
    <div className="mb-6">
      <TableWrapper title="Customs Expense Tracker" headers={headers}>
        {expenses.length === 0 ? (
          <TableRow>
            <TableCell colSpan={5} className="text-center text-purple-400 py-4">No expenses recorded.</TableCell>
          </TableRow>
        ) : (
          expenses.map((e, i) => (
            <TableRow key={i}>
              <TableCell>{e.type}</TableCell>
              <TableCell>₹{e.amount}</TableCell>
              <TableCell>{e.date}</TableCell>
              <TableCell>{e.party}</TableCell>
              <TableCell>{e.billNo}</TableCell>
            </TableRow>
          ))
        )}
      </TableWrapper>
      <div className="bg-white rounded-xl shadow p-6 mt-2">
          <div className="text-purple-900 font-bold text-lg text-right">Total: ₹{total}</div>
      </div>
    </div>
  );
};

export default ExpenseTracker;
