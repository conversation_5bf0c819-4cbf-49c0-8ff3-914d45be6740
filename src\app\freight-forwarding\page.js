"use client";

import React, { useState } from "react";
import { Search, Plus, Download } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import ShipmentSummaryCards from "./ShipmentSummaryCards";
import RecentShipmentsTable from "./RecentShipmentsTable";

export default function FreightForwardingPage() {
  const [searchTerm, setSearchTerm] = useState("");

  const handleNewBooking = () => {
    // TODO: Implement new booking modal
    console.log("New booking clicked");
  };

  const handleExportReport = () => {
    // TODO: Implement export functionality
    console.log("Export report clicked");
  };

  return (
    <div>
      {/* Page Header */}
      <h1 className="text-4xl font-extrabold text-indigo-800 mb-6 flex items-center gap-3 border-b-2 border-indigo-200 pb-4">
        🚢 Freight Forwarding
      </h1>

      {/* Shipment Summary Cards */}
      <ShipmentSummaryCards />

      {/* Controls Section */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 mt-8">
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-purple-400 w-4 h-4" />
            <Input
              type="text"
              placeholder="Search shipments..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-white border-purple-200 focus:border-purple-400 focus:ring-purple-400"
            />
          </div>
        </div>

        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={handleExportReport}
            className="flex items-center gap-2 border-purple-200 text-purple-700 hover:bg-purple-50"
          >
            <Download className="w-4 h-4" />
            Export Report
          </Button>
          <Button
            onClick={handleNewBooking}
            className="flex items-center gap-2 bg-purple-700 hover:bg-purple-800"
          >
            <Plus className="w-4 h-4" />
            New Booking
          </Button>
        </div>
      </div>

      {/* Recent Shipments Table */}
      <RecentShipmentsTable searchTerm={searchTerm} />
    </div>
  );
}
