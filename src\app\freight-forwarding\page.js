"use client";

import React, { useState } from "react";
import { Search, Plus, Download } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { FormModal } from "@/components/common/form";
import ShipmentSummaryCards from "./ShipmentSummaryCards";
import RecentShipmentsTable from "./RecentShipmentsTable";
import ShipmentForm from "./ShipmentForm";

export default function FreightForwardingPage() {
  // Mock data for shipments
  const mockShipments = [
    {
      id: 1,
      shipmentId: "FF-2024-001",
      cargoType: "Electronics",
      origin: "Shanghai, China",
      destination: "Los Angeles, USA",
      vesselName: "MSC Gulsun",
      customerName: "TechCorp Inc.",
      status: "In Transit",
      eta: "2024-01-15",
      bookingDate: "2024-01-01",
      weight: "25.5 MT",
      containerType: "40ft HC",
    },
    {
      id: 2,
      shipmentId: "FF-2024-002",
      cargoType: "Textiles",
      origin: "Mumbai, India",
      destination: "Hamburg, Germany",
      vesselName: "Ever Given",
      customerName: "Fashion Ltd.",
      status: "Booked",
      eta: "2024-01-20",
      bookingDate: "2024-01-02",
      weight: "18.2 MT",
      containerType: "20ft",
    },
    {
      id: 3,
      shipmentId: "FF-2024-003",
      cargoType: "Machinery",
      origin: "Busan, South Korea",
      destination: "Rotterdam, Netherlands",
      vesselName: "OOCL Hong Kong",
      customerName: "Industrial Co.",
      status: "Delivered",
      eta: "2024-01-10",
      bookingDate: "2023-12-28",
      weight: "42.8 MT",
      containerType: "40ft",
    },
    {
      id: 4,
      shipmentId: "FF-2024-004",
      cargoType: "Food Products",
      origin: "Santos, Brazil",
      destination: "New York, USA",
      vesselName: "CMA CGM Marco Polo",
      customerName: "Food Distributors",
      status: "Delayed",
      eta: "2024-01-18",
      bookingDate: "2024-01-03",
      weight: "15.7 MT",
      containerType: "20ft Reefer",
    },
    {
      id: 5,
      shipmentId: "FF-2024-005",
      cargoType: "Automotive Parts",
      origin: "Yokohama, Japan",
      destination: "Long Beach, USA",
      vesselName: "Maersk Madrid",
      customerName: "Auto Parts Inc.",
      status: "In Transit",
      eta: "2024-01-22",
      bookingDate: "2024-01-04",
      weight: "32.1 MT",
      containerType: "40ft HC",
    },
    {
      id: 6,
      shipmentId: "FF-2024-006",
      cargoType: "Chemicals",
      origin: "Antwerp, Belgium",
      destination: "Singapore",
      vesselName: "Hapag-Lloyd Berlin",
      customerName: "ChemCorp",
      status: "Booked",
      eta: "2024-01-25",
      bookingDate: "2024-01-05",
      weight: "28.9 MT",
      containerType: "20ft Tank",
    },
  ];

  const [searchTerm, setSearchTerm] = useState("");
  const [modalOpen, setModalOpen] = useState(false);
  const [shipments, setShipments] = useState(mockShipments);

  const handleNewBooking = () => {
    setModalOpen(true);
  };

  const handleAddShipment = (newShipment) => {
    const shipmentWithId = {
      ...newShipment,
      id: Date.now(),
      shipmentId: `FF-2024-${String(shipments.length + 1).padStart(3, "0")}`,
      bookingDate: new Date().toISOString().split("T")[0],
    };
    setShipments((prev) => [...prev, shipmentWithId]);
    setModalOpen(false);
  };

  const handleExportReport = () => {
    // TODO: Implement export functionality
    console.log("Export report clicked");
  };

  return (
    <div>
      {/* Page Header */}
      <h1 className="text-4xl font-extrabold text-indigo-800 mb-6 flex items-center gap-3 border-b-2 border-indigo-200 pb-4">
        🚢 Freight Forwarding
      </h1>

      {/* Shipment Summary Cards */}
      <ShipmentSummaryCards />

      {/* Controls Section */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 mt-8">
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-purple-400 w-4 h-4" />
            <Input
              type="text"
              placeholder="Search shipments..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-white border-purple-200 focus:border-purple-400 focus:ring-purple-400"
            />
          </div>
        </div>

        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={handleExportReport}
            className="flex items-center gap-2 border-purple-200 text-purple-700 hover:bg-purple-50"
          >
            <Download className="w-4 h-4" />
            Export Report
          </Button>
          <Button
            onClick={handleNewBooking}
            className="flex items-center gap-2 bg-purple-700 hover:bg-purple-800"
          >
            <Plus className="w-4 h-4" />
            New Booking
          </Button>
        </div>
      </div>

      {/* Recent Shipments Table */}
      <RecentShipmentsTable
        searchTerm={searchTerm}
        shipments={shipments}
        setShipments={setShipments}
      />

      {/* Add Shipment Modal */}
      <FormModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        title="New Booking"
      >
        <ShipmentForm onAdd={handleAddShipment} />
      </FormModal>
    </div>
  );
}
