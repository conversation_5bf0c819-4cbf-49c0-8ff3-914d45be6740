import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Tooltip, ResponsiveContainer } from "recharts";

export default function CustomsClearanceDashboard() {
  // Demo data
  const data = [
    { name: "Cleared", value: 18 },
    { name: "Pending", value: 4 },
    { name: "Delayed", value: 2 },
  ];
  const COLORS = ["#22c55e", "#eab308", "#ef4444"];

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const total = data.reduce((sum, entry) => sum + entry.value, 0);
      const percentage = ((payload[0].value / total) * 100).toFixed(2);
      return (
        <div className="custom-tooltip bg-white p-3 border border-gray-300 rounded-lg shadow-md">
          <p className="label text-gray-800 font-semibold">{`${payload[0].name}`}</p>
          <p className="intro text-gray-700">{`Count: ${payload[0].value}`}</p>
          <p className="desc text-gray-600">{`Percentage: ${percentage}%`}</p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="bg-white rounded-2xl shadow-xl p-6 mb-6 transform transition-all duration-300 hover:scale-[1.01] hover:shadow-2xl">
      <h2 className="font-extrabold text-2xl mb-4 flex items-center gap-3 text-indigo-700 border-b border-indigo-100 pb-3">🛃 Customs Clearance Status</h2>
      <div className="flex flex-col md:flex-row justify-between items-center gap-4">
        <div className="flex flex-col gap-2 pl-16 md:w-1/2">
          {data.map((d, idx) => (
            <div key={d.name} className="flex items-center gap-2 text-base font-medium text-gray-700">
              <span className="inline-block w-4 h-4 rounded-full" style={{ background: COLORS[idx] }}></span>
              {d.name}: <span className="font-semibold text-lg">{d.value}</span>
            </div>
          ))}
        </div>
        <div className="h-72 w-full md:w-1/2">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie data={data} dataKey="value" nameKey="name" cx="50%" cy="50%" outerRadius={100} labelLine={false}>
                {data.map((entry, idx) => (
                  <Cell key={`cell-${idx}`} fill={COLORS[idx % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
}

