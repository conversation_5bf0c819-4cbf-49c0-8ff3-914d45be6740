import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Upload } from "lucide-react";

const BranchForm = ({ onAdd, initialData = null, isEdit = false }) => {
  const [formData, setFormData] = useState({
    branchName: initialData?.branchName || "",
    location: initialData?.location || "",
    branchType: initialData?.branchType || "Terminal",
    managerName: initialData?.managerName || "",
    contactPhone: initialData?.contactPhone || "",
    contactEmail: initialData?.contactEmail || "",
    operationalHours: initialData?.operationalHours || "09:00 - 17:00",
    status: initialData?.status || "Active",
    coordinates: initialData?.coordinates || "",
    lastActivity: new Date().toISOString().split('T')[0]
  });

  const [errors, setErrors] = useState({});
  const [mapFile, setMapFile] = useState(null);

  const branchTypes = ["Terminal", "Yard", "Depot", "Office"];
  const statusOptions = ["Active", "Inactive", "Under Maintenance"];

  const operationalHoursOptions = [
    "24/7",
    "06:00 - 22:00",
    "08:00 - 18:00",
    "09:00 - 17:00",
    "Custom"
  ];

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setMapFile(file);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.branchName.trim()) {
      newErrors.branchName = "Branch name is required";
    }

    if (!formData.location.trim()) {
      newErrors.location = "Location is required";
    }

    if (!formData.managerName.trim()) {
      newErrors.managerName = "Manager/Contact person is required";
    }

    if (!formData.contactPhone.trim()) {
      newErrors.contactPhone = "Contact phone is required";
    }

    if (formData.contactEmail && !/\S+@\S+\.\S+/.test(formData.contactEmail)) {
      newErrors.contactEmail = "Please enter a valid email address";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const branchData = {
      ...formData,
      id: initialData?.id || Date.now(),
      lastActivity: new Date().toISOString().split('T')[0],
      mapFile: mapFile ? mapFile.name : initialData?.mapFile || null
    };

    onAdd(branchData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Branch Name */}
      <div className="space-y-2">
        <Label htmlFor="branchName" className="text-sm font-semibold text-purple-700">
          Branch Name *
        </Label>
        <Input
          id="branchName"
          name="branchName"
          type="text"
          value={formData.branchName}
          onChange={handleChange}
          placeholder="Enter branch name"
          className={`bg-white border-purple-200 focus:border-purple-400 focus:ring-purple-400 ${
            errors.branchName ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
          }`}
        />
        {errors.branchName && (
          <p className="text-sm text-red-600">{errors.branchName}</p>
        )}
      </div>

      {/* Location */}
      <div className="space-y-2">
        <Label htmlFor="location" className="text-sm font-semibold text-purple-700">
          Location *
        </Label>
        <Input
          id="location"
          name="location"
          type="text"
          value={formData.location}
          onChange={handleChange}
          placeholder="Enter location (city, state)"
          className={`bg-white border-purple-200 focus:border-purple-400 focus:ring-purple-400 ${
            errors.location ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
          }`}
        />
        {errors.location && (
          <p className="text-sm text-red-600">{errors.location}</p>
        )}
      </div>

      {/* Coordinates (Optional) */}
      <div className="space-y-2">
        <Label htmlFor="coordinates" className="text-sm font-semibold text-purple-700">
          Coordinates (Optional)
        </Label>
        <Input
          id="coordinates"
          name="coordinates"
          type="text"
          value={formData.coordinates}
          onChange={handleChange}
          placeholder="e.g., 19.0760, 72.8777"
          className="bg-white border-purple-200 focus:border-purple-400 focus:ring-purple-400"
        />
      </div>

      {/* Branch Type */}
      <div className="space-y-2">
        <Label htmlFor="branchType" className="text-sm font-semibold text-purple-700">
          Branch Type
        </Label>
        <select
          id="branchType"
          name="branchType"
          value={formData.branchType}
          onChange={handleChange}
          className="w-full px-3 py-2 border border-purple-200 rounded-md bg-white text-purple-700 focus:border-purple-400 focus:ring-2 focus:ring-purple-400 focus:ring-opacity-20 outline-none transition-all"
        >
          {branchTypes.map(type => (
            <option key={type} value={type}>
              {type}
            </option>
          ))}
        </select>
      </div>

      {/* Manager/Contact Person */}
      <div className="space-y-2">
        <Label htmlFor="managerName" className="text-sm font-semibold text-purple-700">
          Manager/Contact Person *
        </Label>
        <Input
          id="managerName"
          name="managerName"
          type="text"
          value={formData.managerName}
          onChange={handleChange}
          placeholder="Enter manager name"
          className={`bg-white border-purple-200 focus:border-purple-400 focus:ring-purple-400 ${
            errors.managerName ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
          }`}
        />
        {errors.managerName && (
          <p className="text-sm text-red-600">{errors.managerName}</p>
        )}
      </div>

      {/* Contact Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="contactPhone" className="text-sm font-semibold text-purple-700">
            Contact Phone *
          </Label>
          <Input
            id="contactPhone"
            name="contactPhone"
            type="tel"
            value={formData.contactPhone}
            onChange={handleChange}
            placeholder="+91-XX-XXXX-XXXX"
            className={`bg-white border-purple-200 focus:border-purple-400 focus:ring-purple-400 ${
              errors.contactPhone ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
            }`}
          />
          {errors.contactPhone && (
            <p className="text-sm text-red-600">{errors.contactPhone}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="contactEmail" className="text-sm font-semibold text-purple-700">
            Contact Email
          </Label>
          <Input
            id="contactEmail"
            name="contactEmail"
            type="email"
            value={formData.contactEmail}
            onChange={handleChange}
            placeholder="<EMAIL>"
            className={`bg-white border-purple-200 focus:border-purple-400 focus:ring-purple-400 ${
              errors.contactEmail ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
            }`}
          />
          {errors.contactEmail && (
            <p className="text-sm text-red-600">{errors.contactEmail}</p>
          )}
        </div>
      </div>

      {/* Operational Hours */}
      <div className="space-y-2">
        <Label htmlFor="operationalHours" className="text-sm font-semibold text-purple-700">
          Operational Hours
        </Label>
        <select
          id="operationalHours"
          name="operationalHours"
          value={formData.operationalHours}
          onChange={handleChange}
          className="w-full px-3 py-2 border border-purple-200 rounded-md bg-white text-purple-700 focus:border-purple-400 focus:ring-2 focus:ring-purple-400 focus:ring-opacity-20 outline-none transition-all"
        >
          {operationalHoursOptions.map(hours => (
            <option key={hours} value={hours}>
              {hours}
            </option>
          ))}
        </select>
      </div>

      {/* Status Toggle */}
      <div className="space-y-2">
        <Label htmlFor="status" className="text-sm font-semibold text-purple-700">
          Status
        </Label>
        <select
          id="status"
          name="status"
          value={formData.status}
          onChange={handleChange}
          className="w-full px-3 py-2 border border-purple-200 rounded-md bg-white text-purple-700 focus:border-purple-400 focus:ring-2 focus:ring-purple-400 focus:ring-opacity-20 outline-none transition-all"
        >
          {statusOptions.map(status => (
            <option key={status} value={status}>
              {status}
            </option>
          ))}
        </select>
      </div>

      {/* Map/Layout Upload */}
      <div className="space-y-2">
        <Label htmlFor="mapFile" className="text-sm font-semibold text-purple-700">
          Branch Map/Layout (Optional)
        </Label>
        <div className="flex items-center gap-3">
          <Input
            id="mapFile"
            type="file"
            accept="image/*,.pdf"
            onChange={handleFileChange}
            className="bg-white border-purple-200 focus:border-purple-400 focus:ring-purple-400"
          />
          <Upload className="w-5 h-5 text-purple-400" />
        </div>
        {mapFile && (
          <p className="text-sm text-green-600">Selected: {mapFile.name}</p>
        )}
      </div>

      {/* Submit Button */}
      <div className="flex justify-end pt-4">
        <Button
          type="submit"
          className="bg-purple-700 hover:bg-purple-800 text-white px-6 py-2"
        >
          {isEdit ? "Update Branch" : "Add Branch"}
        </Button>
      </div>
    </form>
  );
};

export default BranchForm;
