export const headers = [
  "Name",
  "IMO Number",
  "Type",
  "Flag",
  "Gross Tonnage",
  "Status",
  "Actions",
];

export const dummyVessels = [
  {
    vesselName: "Aurora Star",
    imoNumber: "9876543",
    callSign: "AUR123",
    vesselType: "Cargo",
    flag: "Panama",
    grossTonnage: "25000",
    netTonnage: "15000",
    loa: "180",
    beam: "28",
    draught: "9.5",
    dwt: "35000",
    status: "Arrived",
  },
  {
    vesselName: "Oceanic Queen",
    imoNumber: "1234567",
    callSign: "OCE456",
    vesselType: "Tanker",
    flag: "Liberia",
    grossTonnage: "40000",
    netTonnage: "22000",
    loa: "210",
    beam: "32",
    draught: "11.2",
    dwt: "50000",
    status: "Berthed",
  },
  {
    vesselName: "Pacific Trader",
    imoNumber: "7654321",
    callSign: "PAC789",
    vesselType: "Container",
    flag: "Singapore",
    grossTonnage: "35000",
    netTonnage: "20000",
    loa: "195",
    beam: "30",
    draught: "10.1",
    dwt: "42000",
    status: "Unloading",
  },
  {
    vesselName: "Liberty Wave",
    imoNumber: "2468135",
    callSign: "LIB246",
    vesselType: "Bulk Carrier",
    flag: "Marshall Islands",
    grossTonnage: "28000",
    netTonnage: "17000",
    loa: "185",
    beam: "29",
    draught: "9.8",
    dwt: "37000",
    status: "Completed",
  },
  {
    vesselName: "Eastern Spirit",
    imoNumber: "1357924",
    callSign: "EAS135",
    vesselType: "Cargo",
    flag: "Hong Kong",
    grossTonnage: "26000",
    netTonnage: "16000",
    loa: "182",
    beam: "28.5",
    draught: "9.6",
    dwt: "35500",
    status: "Arrived",
  },
  {
    vesselName: "Northern Breeze",
    imoNumber: "9182736",
    callSign: "NOR918",
    vesselType: "LNG Carrier",
    flag: "Bahamas",
    grossTonnage: "47000",
    netTonnage: "28000",
    loa: "230",
    beam: "36",
    draught: "12.4",
    dwt: "60000",
    status: "Completed",
  },
  {
    vesselName: "Western Voyager",
    imoNumber: "8374659",
    callSign: "WES837",
    vesselType: "Ro-Ro",
    flag: "Japan",
    grossTonnage: "30000",
    netTonnage: "18000",
    loa: "190",
    beam: "31",
    draught: "10.5",
    dwt: "40000",
    status: "Unloading",
  },
  {
    vesselName: "Southern Aurora",
    imoNumber: "5647382",
    callSign: "SOU564",
    vesselType: "Container",
    flag: "Malta",
    grossTonnage: "32000",
    netTonnage: "19500",
    loa: "200",
    beam: "30.5",
    draught: "10.2",
    dwt: "43000",
    status: "Berthed",
  },
];


export const statusColors = {
  Arrived:
    "bg-gradient-to-r from-blue-200 to-blue-400 text-blue-900 border-blue-300 shadow-sm",
  Berthed:
    "bg-gradient-to-r from-yellow-200 to-yellow-400 text-yellow-900 border-yellow-300 shadow-sm",
  Unloading:
    "bg-gradient-to-r from-green-200 to-green-400 text-green-900 border-green-300 shadow-sm",
  Completed:
    "bg-gradient-to-r from-gray-200 to-gray-400 text-gray-900 border-gray-300 shadow-sm",
};