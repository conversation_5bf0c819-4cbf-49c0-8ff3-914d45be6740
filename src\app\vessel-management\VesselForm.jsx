import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

const vesselTypes = ["Cargo", "Tanker", "Container", "Bulk Carrier", "Other"];
const flagCountries = [
  "Panama",
  "Liberia",
  "Marshall Islands",
  "Singapore",
  "Hong Kong",
  "Other",
];

const VesselForm = ({ onAdd, initialData = {}, isEdit = false, onClose }) => {
  const [form, setForm] = useState({
    vesselName: initialData.vesselName || "",
    imoNumber: initialData.imoNumber || "",
    callSign: initialData.callSign || "",
    vesselType: initialData.vesselType || vesselTypes[0],
    flag: initialData.flag || flagCountries[0],
    grossTonnage: initialData.grossTonnage || "",
    netTonnage: initialData.netTonnage || "",
    loa: initialData.loa || "",
    beam: initialData.beam || "",
    draught: initialData.draught || "",
    dwt: initialData.dwt || "",
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setForm((f) => ({ ...f, [name]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onAdd({ ...form, status: isEdit ? initialData.status || "Arrived" : "Arrived" });
    if (!isEdit) {
      setForm({
        vesselName: "",
        imoNumber: "",
        callSign: "",
        vesselType: vesselTypes[0],
        flag: flagCountries[0],
        grossTonnage: "",
        netTonnage: "",
        loa: "",
        beam: "",
        draught: "",
        dwt: "",
      });
    } else if (onClose) {
      onClose();
    }
  };

  return (
    <form
      className="grid grid-cols-1 md:grid-cols-2 gap-4"
      onSubmit={handleSubmit}
    >
      <div>
        <Label>Vessel Name</Label>
        <Input
          name="vesselName"
          value={form.vesselName}
          onChange={handleChange}
          placeholder="Enter vessel name"
        />
      </div>
      <div>
        <Label>IMO Number</Label>
        <Input
          name="imoNumber"
          value={form.imoNumber}
          onChange={handleChange}
          placeholder="Enter IMO number"
        />
      </div>
      <div>
        <Label>Call Sign</Label>
        <Input
          name="callSign"
          value={form.callSign}
          onChange={handleChange}
          placeholder="Enter call sign"
        />
      </div>
      <div>
        <Label>Vessel Type</Label>
        <select
          name="vesselType"
          value={form.vesselType}
          onChange={handleChange}
          className="w-full h-10 rounded-md border border-purple-200 px-3 py-2 text-sm"
        >
          {vesselTypes.map((type) => (
            <option key={type}>{type}</option>
          ))}
        </select>
      </div>
      <div>
        <Label>Flag / Country of Registration</Label>
        <select
          name="flag"
          value={form.flag}
          onChange={handleChange}
          className="w-full h-10 rounded-md border border-purple-200 px-3 py-2 text-sm"
        >
          {flagCountries.map((country) => (
            <option key={country}>{country}</option>
          ))}
        </select>
      </div>
      <div>
        <Label>Gross Tonnage</Label>
        <Input
          name="grossTonnage"
          value={form.grossTonnage}
          onChange={handleChange}
          placeholder="Gross Tonnage"
        />
      </div>
      <div>
        <Label>Net Tonnage</Label>
        <Input
          name="netTonnage"
          value={form.netTonnage}
          onChange={handleChange}
          placeholder="Net Tonnage"
        />
      </div>
      <div>
        <Label>Length Overall (LOA) (m)</Label>
        <Input
          name="loa"
          value={form.loa}
          onChange={handleChange}
          placeholder="Length Overall (m)"
        />
      </div>
      <div>
        <Label>Beam (Width) (m)</Label>
        <Input
          name="beam"
          value={form.beam}
          onChange={handleChange}
          placeholder="Beam (m)"
        />
      </div>
      <div>
        <Label>Draught (m)</Label>
        <Input
          name="draught"
          value={form.draught}
          onChange={handleChange}
          placeholder="Draught (m)"
        />
      </div>
      <div>
        <Label>Deadweight Tonnage (DWT)</Label>
        <Input
          name="dwt"
          value={form.dwt}
          onChange={handleChange}
          placeholder="Deadweight Tonnage (DWT)"
        />
      </div>
      <div className="md:col-span-2 flex justify-end">
        <Button type="submit">{isEdit ? "Update Vessel" : "Add Vessel"}</Button>
      </div>
    </form>
  );
};

export default VesselForm;
