import React from "react";
import { TableWrapper, Table, TableRow, TableCell } from "@/components/common/table";
import { cn } from "@/lib/utils";

export default function InternalTransportSection() {
  // Demo data
  const dumperLog = [
    { id: 1, dumper: "D-101", route: "Yard A → Shed B", tonnage: 18, party: "ABC Ltd.", entry: "09:00", exit: "09:30" },
    { id: 2, dumper: "D-102", route: "Dock 1 → Yard C", tonnage: 22, party: "XYZ Exports", entry: "09:15", exit: "09:50" },
  ];
  const activity = [
    { id: 1, dumper: "D-101", activity: "In Transit", time: "09:15" },
    { id: 2, dumper: "D-102", activity: "Unloading", time: "09:45" },
  ];
  return (
    <div className="space-y-6">
      <TableWrapper
        title="🚚 Dumper Entry Log"
        headers={["Dumper ID", "Route", "Tonnage", "Party", "Entry", "Exit"]}
      >
        {dumperLog.map((d, idx) => (
          <TableRow key={d.id} variant={idx % 2 === 0 ? "even" : "odd"}>
            <TableCell>{d.dumper}</TableCell>
            <TableCell>{d.route}</TableCell>
            <TableCell>{d.tonnage}T</TableCell>
            <TableCell>{d.party}</TableCell>
            <TableCell>{d.entry}</TableCell>
            <TableCell>{d.exit}</TableCell>
          </TableRow>
        ))}
      </TableWrapper>

      <TableWrapper
        title="📋 Activity Tracker"
        headers={["Dumper", "Activity", "Timestamp"]}
      >
        {activity.map((a, idx) => (
          <TableRow key={a.id} variant={idx % 2 === 0 ? "even" : "odd"}>
            <TableCell>{a.dumper}</TableCell>
            <TableCell>{a.activity}</TableCell>
            <TableCell>{a.time}</TableCell>
          </TableRow>
        ))}
      </TableWrapper>
    </div>
  );
}
