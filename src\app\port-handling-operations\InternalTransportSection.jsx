import React from "react";
import {
  TableWrapper,
  Table,
  TableRow,
  TableCell,
} from "@/components/common/table";
import { cn } from "@/lib/utils";

export default function InternalCartingSection() {
  // Demo data
  // Labour association: assign a labour team to each dumper log
  const labourTeams = [
    { id: 1, team: "Team A", shift: "Day", role: "Loader", count: 6 },
    { id: 2, team: "Team B", shift: "Night", role: "Operator", count: 4 },
    { id: 3, team: "Team C", shift: "Day", role: "Supervisor", count: 2 },
    { id: 4, team: "Team D", shift: "Night", role: "Helper", count: 5 },
  ];
  // Storage association: assign a storage slot to each dumper log
  const storageSlots = [
    { id: 1, slot: "Yard A-1", area: 100 },
    { id: 2, slot: "Shed B-2", area: 75 },
    { id: 3, slot: "Yard C-3", area: 120 },
    { id: 4, slot: "Dock D-4", area: 90 },
  ];

  const dumperLog = [
    {
      id: 1,
      dumper: "D-101",
      route: "Yard A → Shed B",
      tonnage: 18,
      party: "ABC Ltd.",
      entry: "09:00",
      exit: "09:30",
      labour: labourTeams[0],
      storage: storageSlots[0],
      vesselName: "MV Sea Queen",
      customerName: "ABC Logistics",
      branchName: "Mumbai",
    },
    {
      id: 2,
      dumper: "D-102",
      route: "Dock 1 → Yard C",
      tonnage: 22,
      party: "XYZ Exports",
      entry: "09:15",
      exit: "09:50",
      labour: labourTeams[1],
      storage: storageSlots[1],
      vesselName: "MV Ocean Star",
      customerName: "XYZ Shipping",
      branchName: "Chennai",
    },
    {
      id: 3,
      dumper: "D-103",
      route: "Yard C → Dock D",
      tonnage: 20,
      party: "PQR Inc.",
      entry: "10:00",
      exit: "10:40",
      labour: labourTeams[2],
      storage: storageSlots[2],
      vesselName: "MV Blue Pearl",
      customerName: "PQR Shipping",
      branchName: "Kolkata",
    },
    {
      id: 4,
      dumper: "D-104",
      route: "Dock D → Shed B",
      tonnage: 16,
      party: "LMN Co.",
      entry: "10:30",
      exit: "11:00",
      labour: labourTeams[3],
      storage: storageSlots[3],
      vesselName: "MV Green Bay",
      customerName: "LMN Shipping",
      branchName: "Cochin",
    },
  ];
  const activity = [
    { id: 1, dumper: "D-101", activity: "In Transit", time: "09:15" },
    { id: 2, dumper: "D-102", activity: "Unloading", time: "09:45" },
    { id: 3, dumper: "D-103", activity: "Waiting", time: "10:10" },
    { id: 4, dumper: "D-104", activity: "Loading", time: "10:35" },
  ];
  return (
    <div className="space-y-6">
      <TableWrapper
        title="🚚 Dumper Entry Log"
        headers={[
          "Dumper ID",
          "Route",
          "Tonnage",
          "Party",
          "Entry",
          "Exit",
          "Labour Team",
          "Storage Slot",
        ]}
      >
        {dumperLog.map((d, idx) => (
          <TableRow key={d.id} variant={idx % 2 === 0 ? "even" : "odd"}>
            <TableCell>{d.dumper}</TableCell>
            <TableCell>{d.route}</TableCell>
            <TableCell>{d.tonnage}T</TableCell>
            <TableCell>{d.party}</TableCell>
            <TableCell>{d.entry}</TableCell>
            <TableCell>{d.exit}</TableCell>
            <TableCell>
              {d.labour.team} ({d.labour.role}, {d.labour.shift},{" "}
              {d.labour.count})
            </TableCell>
            <TableCell>
              {d.storage.slot} ({d.storage.area} m²)
            </TableCell>
          </TableRow>
        ))}
      </TableWrapper>

      <TableWrapper
        title="📋 Activity Tracker"
        headers={["Dumper", "Activity", "Timestamp"]}
      >
        {activity.map((a, idx) => (
          <TableRow key={a.id} variant={idx % 2 === 0 ? "even" : "odd"}>
            <TableCell>{a.dumper}</TableCell>
            <TableCell>{a.activity}</TableCell>
            <TableCell>{a.time}</TableCell>
          </TableRow>
        ))}
      </TableWrapper>
    </div>
  );
}
