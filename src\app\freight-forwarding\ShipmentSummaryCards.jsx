import React from "react";
import { Ship, Package, CheckCircle, AlertTriangle } from "lucide-react";

const ShipmentSummaryCards = () => {
  const summaryData = [
    {
      title: "Booked",
      value: "24",
      icon: Package,
      color: "blue",
      description: "Total booked shipments",
      bgGradient: "from-blue-500 to-blue-600",
      bgLight: "bg-blue-50",
      textColor: "text-blue-700",
      iconColor: "text-blue-600"
    },
    {
      title: "In Transit",
      value: "18",
      icon: Ship,
      color: "purple",
      description: "Active shipments",
      bgGradient: "from-purple-500 to-purple-600",
      bgLight: "bg-purple-50",
      textColor: "text-purple-700",
      iconColor: "text-purple-600"
    },
    {
      title: "Delivered",
      value: "156",
      icon: CheckCircle,
      color: "green",
      description: "Completed deliveries",
      bgGradient: "from-green-500 to-green-600",
      bgLight: "bg-green-50",
      textColor: "text-green-700",
      iconColor: "text-green-600"
    },
    {
      title: "Delayed",
      value: "3",
      icon: Alert<PERSON>riangle,
      color: "red",
      description: "Delayed shipments",
      bgGradient: "from-red-500 to-red-600",
      bgLight: "bg-red-50",
      textColor: "text-red-700",
      iconColor: "text-red-600"
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {summaryData.map((item, index) => {
        const IconComponent = item.icon;
        return (
          <div
            key={index}
            className={`${item.bgLight} rounded-2xl shadow-xl p-6 transform transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl border border-white/50`}
          >
            <div className="flex items-center justify-between mb-4">
              <div className={`p-3 rounded-xl bg-gradient-to-r ${item.bgGradient} shadow-lg`}>
                <IconComponent className="w-6 h-6 text-white" />
              </div>
              <div className="text-right">
                <div className={`text-3xl font-extrabold ${item.textColor}`}>
                  {item.value}
                </div>
              </div>
            </div>
            <div>
              <h3 className={`text-lg font-bold ${item.textColor} mb-1`}>
                {item.title}
              </h3>
              <p className="text-sm text-gray-600 font-medium">
                {item.description}
              </p>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ShipmentSummaryCards;
