import axios from "axios";

const apiCall = async (method, url, data, config = {}) => {
  const response = await axios({ method, url, data, ...config });
  return response.data;
};

export const apiGet = (url, config = {}) =>
  apiCall("get", url, undefined, config);

export const apiPost = (url, data = {}, config = {}) =>
  apiCall("post", url, data, config);

export const apiPut = (url, data = {}, config = {}) =>
  apiCall("put", url, data, config);

export const apiDelete = (url, config = {}) =>
  apiCall("delete", url, undefined, config);
