import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer } from "recharts";

export default function StorageOccupancyDashboard() {
  // Demo data
  const data = [
    { area: "Yard A", occupied: 460, total: 670, color: "#3b82f6" },
    { area: "Shed B", occupied: 320, total: 500, color: "#22c55e" },
    { area: "Yard C", occupied: 780, total: 900, color: "#eab308" },
    { area: "Shed D", occupied: 180, total: 450, color: "#a21caf" },
  ];

  const processedData = data.map(item => ({
    ...item,
    percentage: (item.occupied / item.total) * 100,
  }));

  return (
    <div className="bg-white rounded-xl shadow p-5 mb-6 w-full">
      <h2 className="font-bold text-lg mb-4 flex items-center gap-2 text-gray-800">
        <span className="text-2xl">🏗️</span> Storage Occupancy
      </h2>
      <div className="flex flex-col md:flex-row items-center justify-between w-full">
        <div className="flex flex-col gap-4 mb-4 md:mb-0 md:w-1/2 md:ml-4 pl-16">
          {processedData.map((d, idx) => (
            <div key={d.area} className="flex items-center gap-2 text-base font-medium text-gray-700">
              <span className="inline-block w-4 h-4 rounded-full" style={{ background: d.color }}></span>
              {d.area}: {d.occupied} out of {d.total}
            </div>
          ))}
        </div>
        <div className="h-48 w-full md:w-1/2">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={processedData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <XAxis dataKey="area" axisLine={false} tickLine={false} />
              <YAxis axisLine={false} tickLine={false} domain={[0, 100]} />
              <Tooltip formatter={(value, name, props) => [`${value.toFixed(2)}%`, "Occupancy"]} />
              <Bar dataKey="percentage" fill={(entry) => entry.color} name="Occupancy" barSize={30} radius={[5, 5, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
}
