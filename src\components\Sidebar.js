"use client";

import Link from "next/link";
import { usePathname, useSearchParams } from "next/navigation";
import { MODULES, PortModules } from "@/utils/modules";
import * as Icons from "lucide-react";

export default function Sidebar() {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const branch = searchParams.get("branch");
  const modulesToShow = branch ? PortModules : MODULES;
  return (
    <aside
      className="w-72 h-screen min-h-0 bg-gradient-to-br from-purple-50 to-indigo-100 border-r border-indigo-200 shadow-2xl flex flex-col p-6 gap-4 relative transform transition-all duration-300 ease-in-out hover:shadow-3xl"
      style={{
        maxHeight: "100vh",
        height: "100dvh",
        minHeight: 0,
        overflowY: "auto",
      }}
    >
      <div className="flex items-center gap-3 mb-10">
        <span className="inline-flex items-center justify-center bg-gradient-to-tr from-indigo-700 to-purple-500 text-white rounded-2xl px-4 py-2 text-2xl font-black shadow-lg tracking-tight drop-shadow-md border-4 border-white transform hover:scale-105 transition-transform duration-200">
          ACT
        </span>
        <span className="text-3xl font-extrabold text-indigo-900 tracking-tight drop-shadow-sm">
          Ports
        </span>
      </div>
      <nav className="flex flex-col gap-2">
        {modulesToShow.map((mod) => {
          const LucideIcon = Icons[mod.icon] || Icons.Circle;
          const isActive = pathname === mod.route;
          return (
            <Link
              key={mod.route}
              href={mod.route}
              prefetch={true}
              className={`flex items-center gap-4 px-5 py-3 rounded-xl font-semibold text-sm transition-all duration-300 ease-in-out border border-transparent group shadow-md
                ${
                  isActive
                    ? "bg-gradient-to-r from-indigo-600 to-purple-500 text-white shadow-lg border-indigo-700 scale-[1.03] transform hover:scale-105"
                    : "text-gray-700 bg-white/80 hover:bg-indigo-50/60 hover:text-indigo-800 hover:shadow-lg"
                }
              `}
              style={{
                boxShadow: isActive
                  ? "0 8px 25px 0 rgba(99, 102, 241, 0.3)"
                  : undefined,
              }}
            >
              <LucideIcon
                className={`w-6 h-6 transition-colors duration-300 ${
                  isActive
                    ? "text-white"
                    : "text-indigo-400 group-hover:text-indigo-600"
                }`}
              />
              <span className="truncate">{mod.name}</span>
            </Link>
          );
        })}
      </nav>
      <div className="mt-auto">
        <button
          onClick={() => (window.location.href = "/login")}
          className="w-full flex items-center justify-center mt-4 gap-4 px-5 py-3 rounded-xl font-semibold text-sm transition-all duration-300 ease-in-out border border-transparent group shadow-md
            bg-red-500 text-white hover:bg-red-600 shadow-lg border-red-700 transform hover:scale-105"
        >
          <Icons.LogOut className="w-6 h-6 text-white" />
          <span>Logout</span>
        </button>
      </div>
      <div className="text-xs text-indigo-400 text-center select-none font-semibold tracking-wide">
        &copy; {new Date().getFullYear()} Port Shipping
      </div>
    </aside>
  );
}
