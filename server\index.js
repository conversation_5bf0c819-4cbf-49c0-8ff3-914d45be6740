// server/index.js
import express from "express";
import cors from "cors";

import apiRoutes from "./routes/index.js";
import config from "./config/env.js";

const app = express();
const port = config.port || 3000;

// Middlewares
app.use(cors());
app.use(express.json());

// Health check route
app.get("/", (req, res) => {
  res.status(200).json({ status: "ok", message: "ACT Ports API running" });
});

// API routes
app.use("/api", apiRoutes);

// Global error handler
app.use((err, req, res, next) => {
  console.error("Global error handler:", err);
  res.status(500).json({ error: "Internal server error" });
});

// Boot the server and run migrations
const startServer = async () => {
  try {
    await import("./migrations/createTables.js");
    app.listen(port, () => {
      console.log(`🚀 Server is running at http://localhost:${port}`);
    });
  } catch (err) {
    console.error("❌ Failed to start server:", err);
    process.exit(1);
  }
};

startServer();
