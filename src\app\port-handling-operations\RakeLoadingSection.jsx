import React from "react";
import {
  TableWrapper,
  Table,
  TableRow,
  TableCell,
} from "@/components/common/table";
import { cn } from "@/lib/utils";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";

export default function RakeLoadingSection() {
  // Demo data
  const rakes = [
    {
      id: 1,
      number: "R-001",
      date: "2025-06-27",
      qty: 120,
      dest: "Delhi",
      labour: 10,
      equipment: 3,
      hours: 5,
      status: "Completed",
    },
    {
      id: 2,
      number: "R-002",
      date: "2025-06-27",
      qty: 90,
      dest: "Mumbai",
      labour: 8,
      equipment: 2,
      hours: 4,
      status: "In Progress",
    },
  ];
  const statusColors = {
    Completed: "bg-green-100 text-green-800",
    "In Progress": "bg-yellow-100 text-yellow-800",
  };

  const mixingSegregationData = [
    {
      name: "Day 1",
      "Segregation Completed": 150,
      "Blending In Progress": 100,
    },
    {
      name: "Day 2",
      "Segregation Completed": 180,
      "Blending In Progress": 120,
    },
    { name: "Day 3", "Segregation Completed": 120, "Blending In Progress": 90 },
    {
      name: "Day 4",
      "Segregation Completed": 200,
      "Blending In Progress": 150,
    },
    {
      name: "Day 5",
      "Segregation Completed": 170,
      "Blending In Progress": 110,
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <div className="overflow-x-auto">
          <TableWrapper
            title="🚂 Rake Details"
            headers={[
              "Rake No.",
              "Date",
              "Qty",
              "Destination",
              "Labour",
              "Equipment",
              "Hours",
              "Status",
            ]}
          >
            {rakes.map((r) => (
              <TableRow key={r.id}>
                <TableCell>{r.number}</TableCell>
                <TableCell>{r.date}</TableCell>
                <TableCell>{r.qty}</TableCell>
                <TableCell>{r.dest}</TableCell>
                <TableCell>{r.labour}</TableCell>
                <TableCell>{r.equipment}</TableCell>
                <TableCell>{r.hours}h</TableCell>
                <TableCell>
                  <span
                    className={cn(
                      "px-2 py-1 rounded text-xs font-medium",
                      statusColors[r.status]
                    )}
                  >
                    {r.status}
                  </span>
                </TableCell>
              </TableRow>
            ))}
          </TableWrapper>
        </div>
      </div>
      <div className="bg-gradient-to-br from-purple-50 to-indigo-100 rounded-xl shadow-lg p-6 border border-indigo-100 mt-2">
        <h2 className="font-black text-2xl flex items-center gap-2 mb-4 text-indigo-900 drop-shadow-sm tracking-tight">
          🧪 Mixing / Segregation Logs
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
          <div className="bg-yellow-100 rounded-2xl p-6 flex flex-col items-center shadow-md border border-yellow-200">
            <div className="text-lg font-bold text-yellow-800 mb-1">
              Segregation Completed
            </div>
            <p className="text-4xl font-extrabold text-yellow-700 drop-shadow">
              {mixingSegregationData.reduce(
                (sum, item) => sum + item["Segregation Completed"],
                0
              )}
            </p>
          </div>
          <div className="bg-blue-100 rounded-2xl p-6 flex flex-col items-center shadow-md border border-blue-200">
            <div className="text-lg font-bold text-blue-800 mb-1">
              Blending In Progress
            </div>
            <p className="text-4xl font-extrabold text-blue-700 drop-shadow">
              {mixingSegregationData.reduce(
                (sum, item) => sum + item["Blending In Progress"],
                0
              )}
            </p>
          </div>
        </div>
        <div className="bg-white rounded-2xl shadow-lg p-6 mt-2 border border-indigo-100">
          <h3 className="font-bold text-lg mb-4 text-indigo-800 flex items-center gap-2">
            <span className="text-indigo-400">📊</span> Daily Progress
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart
              data={mixingSegregationData}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <XAxis
                dataKey="name"
                tick={{ fill: "#6366f1", fontWeight: 600 }}
              />
              <YAxis tick={{ fill: "#6366f1", fontWeight: 600 }} />
              <Tooltip />
              <Legend />
              <Bar
                dataKey="Segregation Completed"
                stackId="a"
                fill="#facc15"
                radius={[8, 8, 0, 0]}
              />
              <Bar
                dataKey="Blending In Progress"
                stackId="a"
                fill="#60a5fa"
                radius={[8, 8, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
}
