import React from "react";

export const expenseTabs = [
  {
    key: "labour",
    label: "Labour",
    icon: "👷",
    component: React.lazy(() => import("./LabourExpenseCard")),
  },
  {
    key: "equipment",
    label: "Equipment Rental",
    icon: "🚜",
    component: React.lazy(() => import("./EquipmentExpenseCard")),
  },
  {
    key: "transport",
    label: "Transport",
    icon: "🚚",
    component: React.lazy(() => import("./TransportExpenseCard")),
  },
  {
    key: "storage",
    label: "Storage",
    icon: "🏗️",
    component: React.lazy(() => import("./StorageExpenseCard")),
  },
  {
    key: "misc",
    label: "Misc. Charges",
    icon: "🧾",
    component: React.lazy(() => import("./MiscExpenseCard")),
  },
];

export default expenseTabs;
