"use client";

import "./globals.css";
import Sidebar from "@/components/Sidebar";
import { usePathname } from "next/navigation";

const metadata = {
  title: "Port Shipping App",
  description: "Port Shipping Management System",
};

export default function RootLayout({ children }) {
  const pathname = usePathname();
  const isLoginPage = pathname === "/login";

  return (
    <html lang="en">
      <body className="min-h-screen h-screen w-full overflow-hidden">
        <div className="flex h-screen w-full">
          {!isLoginPage && <Sidebar />}
          <main
            className={`flex-1 h-screen overflow-y-auto ${
              isLoginPage
                ? "bg-gray-100"
                : "p-6 bg-gradient-to-br from-blue-50 to-blue-200"
            }`}
          >
            <div
              className={
                isLoginPage
                  ? ""
                  : "w-full mx-auto py-10 px-4 bg-gray-50 rounded-xl shadow-lg"
              }
            >
              {children}
            </div>
          </main>
        </div>
      </body>
    </html>
  );
}
